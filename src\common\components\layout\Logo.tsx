import React from 'react';
import { Link } from 'react-router-dom';

/**
 * Props for the Logo component
 * @interface LogoProps
 */
interface LogoProps {
  /**
   * Color variant of the logo
   * @default 'default'
   */
  variant?: 'default' | 'white';

  /**
   * Whether to show the text "TradeChampionX" next to the logo
   * @default true
   */
  showText?: boolean;

  /**
   * Size of the logo
   * @default 'medium'
   */
  size?: 'small' | 'medium' | 'large';

  /**
   * Whether to wrap the logo in a Link component that navigates to the home page
   * @default true
   */
  linkWrapper?: boolean;
}

/**
 * Logo component for TradeChampionX
 *
 * Displays the TradeChampionX logo with optional text. The logo features a gradient background
 * with an animated trading chart line. The component can be configured with different sizes,
 * color variants, and can optionally be wrapped in a Link component for navigation.
 *
 * @component
 * @version 1.0.0
 * @status stable
 *
 * @example
 * // Basic usage
 * <Logo />
 *
 * @example
 * // White variant without text
 * <Logo variant="white" showText={false} />
 *
 * @example
 * // Large size without link wrapper
 * <Logo size="large" linkWrapper={false} />
 */
const Logo: React.FC<LogoProps> = ({
  variant = 'default',
  showText = true,
  size = 'medium',
  linkWrapper = true
}) => {
  /**
   * Size classes for the logo and text based on the size prop
   * @type {Object}
   */
  const sizeClasses = {
    small: {
      logo: "h-8 w-8",
      text: "text-lg"
    },
    medium: {
      logo: "h-10 w-10",
      text: "text-xl"
    },
    large: {
      logo: "h-14 w-14",
      text: "text-2xl"
    }
  };

  /**
   * Text color class based on the variant prop
   * @type {string}
   */
  const textColorClass = variant === 'white'
    ? "text-white"
    : "bg-gradient-to-r from-forex-dark to-forex-primary bg-clip-text text-transparent";

  /**
   * Internal component that renders the logo content without the Link wrapper
   * @returns {JSX.Element} The logo content
   */
  const LogoContent = () => (
    <div className="flex items-center space-x-2">
      <div className={`${sizeClasses[size].logo} rounded-full bg-gradient-to-br from-forex-primary to-forex-secondary flex items-center justify-center text-white font-bold shadow-lg relative overflow-hidden group`}>
        {/* Background animation effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-forex-primary via-forex-secondary to-forex-accent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

        {/* Animated trading chart line */}
        <div className="absolute inset-0 flex items-center justify-center opacity-30">
          <svg width="80%" height="40%" viewBox="0 0 100 40" preserveAspectRatio="none">
            <path
              d="M0,20 L20,10 L40,25 L60,5 L80,15 L100,10"
              fill="none"
              stroke="rgba(255,255,255,0.8)"
              strokeWidth="2"
              className="animate-pulse-slow"
            />
          </svg>
        </div>

        {/* Logo text */}
        <span className="relative z-10">TX</span>
      </div>

      {showText && (
        <span className={`${sizeClasses[size].text} font-bold ${textColorClass} ml-1`}>
          TradeChampionX
        </span>
      )}
    </div>
  );

  return linkWrapper ? (
    <Link to="/" className="no-underline">
      <LogoContent />
    </Link>
  ) : (
    <LogoContent />
  );
};

export default Logo;
