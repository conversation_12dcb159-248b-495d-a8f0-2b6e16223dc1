
import { useEffect } from "react";
import { Button } from "@/common/components/ui";
import { Shield, Clock, Zap, Wallet } from "lucide-react";

const Index = () => {
  useEffect(() => {
    // Scroll to section if hash is present in URL
    if (window.location.hash) {
      const id = window.location.hash.substring(1);
      const element = document.getElementById(id);
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth'
        });
      }
    }
  }, []);

  return (
    <div className="min-h-screen flex flex-col bg-gray-900 text-white">
      {/* Simple Header */}
      <header className="bg-gray-800 p-4">
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="text-2xl font-bold">TradeChampionX</h1>
          <Button variant="outline">Get Started</Button>
        </div>
      </header>

      <main className="flex-grow">
        {/* Simple Hero Section */}
        <section className="py-20 px-4 text-center">
          <div className="container mx-auto max-w-4xl">
            <h1 className="text-5xl font-bold mb-6">
              Welcome to TradeChampionX
            </h1>
            <p className="text-xl mb-8 text-gray-300">
              Fair Trading Challenges with Guaranteed Payouts
            </p>
            <div className="flex justify-center gap-4 mb-12">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                Join Challenge
              </Button>
              <Button size="lg" variant="outline">
                Learn More
              </Button>
            </div>

            {/* Feature Icons */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16">
              <div className="flex flex-col items-center">
                <Shield className="w-12 h-12 mb-4 text-blue-400" />
                <h3 className="font-semibold">Secure</h3>
              </div>
              <div className="flex flex-col items-center">
                <Clock className="w-12 h-12 mb-4 text-green-400" />
                <h3 className="font-semibold">Fast</h3>
              </div>
              <div className="flex flex-col items-center">
                <Zap className="w-12 h-12 mb-4 text-yellow-400" />
                <h3 className="font-semibold">Powerful</h3>
              </div>
              <div className="flex flex-col items-center">
                <Wallet className="w-12 h-12 mb-4 text-purple-400" />
                <h3 className="font-semibold">Profitable</h3>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Simple Footer */}
      <footer className="bg-gray-800 p-4 text-center">
        <p className="text-gray-400">© 2024 TradeChampionX. All rights reserved.</p>
      </footer>
    </div>
  );
};

export default Index;
