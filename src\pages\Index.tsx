
import React, { useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/common/components/layout";
import {
  Hero,
  HowItWorks,
  PricingTable,
  FAQSection,
  TrustPillars,
  FeatureShowcase,
  GamificationSection,
  DiscordSection,
  PrizeDistributionExplainer
} from "@/features/landing";
import { But<PERSON> } from "@/common/components/ui";
import { Shield, Clock, Zap, Wallet } from "lucide-react";

const Index = () => {
  useEffect(() => {
    // Initialize AOS library for scroll animations
    if (typeof window !== 'undefined' && window.AOS) {
      window.AOS.init({
        duration: 800,
        once: false,
        mirror: true
      });
    }

    // Scroll to section if hash is present in URL
    if (window.location.hash) {
      const id = window.location.hash.substring(1);
      const element = document.getElementById(id);
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth'
        });
      }
    }
  }, []);

  return (
    <div className="min-h-screen flex flex-col bg-forex-dark">
      <Header />

      <main className="flex-grow">
        <Hero />
        <HowItWorks />
        <TrustPillars />
        <FeatureShowcase />
        <PrizeDistributionExplainer />
        <GamificationSection />
        <div id="pricing">
          <PricingTable />
        </div>
        <div id="faq">
          <FAQSection />
        </div>
        <DiscordSection />

        {/* Improved Final CTA Section with consistent styling */}
        <section className="py-24 relative overflow-hidden bg-gradient-to-b from-forex-dark to-[#0a1a2f]">
          {/* Consistent background pattern */}
          <div className="absolute inset-0 bg-[url('/bg-pattern.svg')] opacity-10"></div>

          {/* Consistent gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-forex-primary/5 to-forex-accent/5"></div>

          {/* Consistent decorative elements */}
          <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-forex-primary/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-forex-accent/5 rounded-full blur-3xl"></div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-6xl mx-auto">
              {/* Improved card with consistent styling */}
              <div className="bg-forex-card/80 backdrop-blur-md rounded-2xl border border-forex-border/20 shadow-xl overflow-hidden">
                <div className="grid grid-cols-1 lg:grid-cols-5 gap-0">
                  {/* Left content - Stats with consistent styling */}
                  <div className="lg:col-span-2 bg-gradient-to-br from-forex-primary/10 to-forex-accent/10 p-8 lg:p-10">
                    <div className="h-full flex flex-col justify-between">
                      <div>
                        <h3 className="text-2xl md:text-3xl font-bold text-white mb-8">
                          Prove Your Skills & <span className="bg-gradient-to-r from-forex-primary to-forex-accent bg-clip-text text-transparent">Win Big</span>
                        </h3>
                      </div>

                      <div className="space-y-8">
                        {/* Consistent icon styling */}
                        <div className="flex items-start">
                          <div className="w-12 h-12 rounded-xl bg-forex-primary/20 flex items-center justify-center mr-5 flex-shrink-0">
                            <Shield className="h-6 w-6 text-forex-primary" />
                          </div>
                          <div>
                            <h4 className="text-white font-bold text-xl mb-1">75% Community Rewards</h4>
                            <p className="text-white/70 text-base">Entry fees go directly to top performers</p>
                          </div>
                        </div>

                        <div className="flex items-start">
                          <div className="w-12 h-12 rounded-xl bg-forex-primary/20 flex items-center justify-center mr-5 flex-shrink-0">
                            <Clock className="h-6 w-6 text-forex-primary" />
                          </div>
                          <div>
                            <h4 className="text-white font-bold text-xl mb-1">Next-Day Payouts</h4>
                            <p className="text-white/70 text-base">No waiting games or hidden clauses</p>
                          </div>
                        </div>

                        <div className="flex items-start">
                          <div className="w-12 h-12 rounded-xl bg-forex-primary/20 flex items-center justify-center mr-5 flex-shrink-0">
                            <Zap className="h-6 w-6 text-forex-primary" />
                          </div>
                          <div>
                            <h4 className="text-white font-bold text-xl mb-1">Daily Challenges</h4>
                            <p className="text-white/70 text-base">Compete and win every 24 hours</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right content - Improved CTA */}
                  <div className="lg:col-span-3 p-8 lg:p-12 flex flex-col justify-center bg-forex-card/40">
                    <div className="max-w-lg mx-auto">
                      <h2 className="text-3xl md:text-4xl font-bold text-white mb-6" data-aos="fade-up">
                        Ready to Join the <span className="bg-gradient-to-r from-forex-primary to-forex-accent bg-clip-text text-transparent">Champions?</span>
                      </h2>

                      <p className="text-lg text-white/80 mb-8" data-aos="fade-up" data-aos-delay="100">
                        Start trading today and prove your skills in our next challenge. Top 30% always get rewarded!
                      </p>

                      <div data-aos="fade-up" data-aos-delay="200">
                        {/* Consistent button styling */}
                        <Button
                          size="lg"
                          className="bg-forex-primary hover:bg-forex-hover text-white font-semibold text-lg px-8 py-7 shadow-lg transition-all duration-300 w-full rounded-xl"
                          onClick={() => document.getElementById('pricing')?.scrollIntoView({behavior: 'smooth'})}
                        >
                          Start Your Challenge Now
                        </Button>

                        {/* Improved trustworthy badges */}
                        <div className="flex flex-wrap items-center justify-center mt-6 gap-4">
                          <div className="flex items-center bg-white/10 px-4 py-2 rounded-lg border border-white/10">
                            <Shield className="h-5 w-5 mr-2 text-blue-400" />
                            <span className="text-sm text-white/90 font-medium">cTrader Verified</span>
                          </div>

                          <div className="flex items-center bg-white/10 px-4 py-2 rounded-lg border border-white/10">
                            <Wallet className="h-5 w-5 mr-2 text-green-400" />
                            <span className="text-sm text-white/90 font-medium">Crypto Payments</span>
                          </div>

                          <div className="flex items-center bg-white/10 px-4 py-2 rounded-lg border border-white/10">
                            <Shield className="h-5 w-5 mr-2 text-forex-primary" />
                            <span className="text-sm text-white/90 font-medium">Secure Trading</span>
                          </div>
                        </div>


                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Index;
