/**
 * Socket Service
 * @description Service for managing Socket.IO client connection and events
 * @version 1.0.0
 * @status stable
 */

import { io, Socket } from 'socket.io-client';
import { toast } from 'sonner';

// Socket event names
export enum SocketEvent {
  // Connection events
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  ERROR = 'error',
  RECONNECT = 'reconnect',
  RECONNECT_ATTEMPT = 'reconnect_attempt',
  RECONNECT_ERROR = 'reconnect_error',
  RECONNECT_FAILED = 'reconnect_failed',

  // Authentication events
  AUTHENTICATE = 'authenticate',
  AUTHENTICATION_ERROR = 'authentication_error',
  AUTHENTICATION_SUCCESS = 'authentication_success',

  // Trade events
  TRADE_CREATED = 'trade:created',
  TRADE_UPDATED = 'trade:updated',

  // Leaderboard events
  LEADERBOARD_UPDATED = 'leaderboard:updated',

  // Notification events
  NOTIFICATION_CREATED = 'notification:created',
  NOTIFICATION_READ = 'notification:read',

  // User metrics events
  USER_METRICS_UPDATED = 'user:metrics:updated',

  // Challenge events
  CHALLENGE_STARTED = 'challenge:started',
  CHALLENGE_ENDED = 'challenge:ended',
  CHALLENGE_ENTRY_DISQUALIFIED = 'challenge:entry:disqualified',

  // cTrader events
  CTRADER_CONNECTED = 'ctrader:connected',
  CTRADER_DISCONNECTED = 'ctrader:disconnected',
  CTRADER_TOKEN_EXPIRED = 'ctrader:token:expired',
  DRAWDOWN_WARNING = 'drawdown:warning',
}

// Socket service singleton
class SocketService {
  private socket: Socket | null = null;
  private eventHandlers: Map<string, Set<(data: any) => void>> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 2000; // 2 seconds
  private isConnecting = false;
  private token: string | null = null;

  /**
   * Initialize the socket connection
   * @param token - JWT token for authentication
   */
  public init(token: string): void {
    if (this.socket || this.isConnecting) {
      console.log('Socket already initialized or connecting');
      return;
    }

    this.token = token;
    this.isConnecting = true;

    try {
      // Get the API URL from environment variables
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5003/api';
      const baseUrl = apiUrl.replace('/api', '');
      console.log('Socket connecting to:', baseUrl);

      // Create the socket connection with more detailed error handling
      this.socket = io(baseUrl, {
        auth: { token },
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
        timeout: 15000, // Increased timeout
        transports: ['websocket', 'polling'], // Try WebSocket first, then fall back to polling
      });

      // Add connection error handler
      this.socket.on('connect_error', (error) => {
        console.error('Socket connection error:', error.message);

        // If the error is related to authentication, show a more specific message
        if (error.message.includes('Authentication error')) {
          console.warn('Socket authentication error. Real-time updates will be unavailable.');
          // Don't show toast to avoid overwhelming the user
          // toast.error('Authentication failed. Please try logging out and back in.');
        } else {
          console.warn('Socket connection error. Real-time updates will be unavailable.');
          // Don't show toast to avoid overwhelming the user
          // toast.error('Connection error. Some features may be unavailable.');
        }
      });
    } catch (error) {
      console.error('Error initializing socket:', error);
      this.isConnecting = false;
      toast.error('Failed to initialize real-time connection');
    }

    console.log('Socket connection created');

    // Set up event handlers
    this.setupEventHandlers();

    this.isConnecting = false;
  }

  /**
   * Set up socket event handlers
   */
  private setupEventHandlers(): void {
    if (!this.socket) {
      return;
    }

    // Connection events
    this.socket.on(SocketEvent.CONNECT, () => {
      console.log('Socket connected');
      this.reconnectAttempts = 0;
    });

    this.socket.on(SocketEvent.DISCONNECT, (reason) => {
      console.log(`Socket disconnected: ${reason}`);
    });

    this.socket.on(SocketEvent.ERROR, (error) => {
      console.error('Socket error:', error);
      toast.error('Connection error. Please try again later.');
    });

    this.socket.on(SocketEvent.RECONNECT_ATTEMPT, (attempt) => {
      console.log(`Socket reconnect attempt ${attempt}`);
      this.reconnectAttempts = attempt;
    });

    this.socket.on(SocketEvent.RECONNECT_ERROR, (error) => {
      console.error('Socket reconnect error:', error);
    });

    this.socket.on(SocketEvent.RECONNECT_FAILED, () => {
      console.error('Socket reconnect failed');
      toast.error('Connection failed. Please refresh the page.');
    });

    this.socket.on(SocketEvent.RECONNECT, (attempt) => {
      console.log(`Socket reconnected after ${attempt} attempts`);
      toast.success('Connection restored');
    });

    // Authentication events
    this.socket.on(SocketEvent.AUTHENTICATION_ERROR, (error) => {
      console.error('Socket authentication error:', error);
      toast.error('Authentication error. Please log in again.');
    });

    this.socket.on(SocketEvent.AUTHENTICATION_SUCCESS, (data) => {
      console.log('Socket authenticated:', data);
    });

    // Notification events
    this.socket.on(SocketEvent.NOTIFICATION_CREATED, (notification) => {
      console.log('Notification received:', notification);
      toast(notification.title, {
        description: notification.description,
      });
      this.emitEvent(SocketEvent.NOTIFICATION_CREATED, notification);
    });

    // Challenge events
    this.socket.on(SocketEvent.CHALLENGE_ENTRY_DISQUALIFIED, (data) => {
      console.log('Challenge entry disqualified:', data);
      toast.error(`You have been disqualified from the challenge: ${data.reason}`);
      this.emitEvent(SocketEvent.CHALLENGE_ENTRY_DISQUALIFIED, data);
    });

    // cTrader events
    this.socket.on(SocketEvent.CTRADER_CONNECTED, (data) => {
      console.log('cTrader connected:', data);
      this.emitEvent(SocketEvent.CTRADER_CONNECTED, data);
    });

    this.socket.on(SocketEvent.CTRADER_DISCONNECTED, (data) => {
      console.log('cTrader disconnected:', data);
      this.emitEvent(SocketEvent.CTRADER_DISCONNECTED, data);
    });

    this.socket.on(SocketEvent.CTRADER_TOKEN_EXPIRED, (data) => {
      console.log('cTrader token expired:', data);
      toast.error('Your cTrader token has expired. Please reconnect your account.');
      this.emitEvent(SocketEvent.CTRADER_TOKEN_EXPIRED, data);
    });

    // Drawdown warning event
    this.socket.on(SocketEvent.DRAWDOWN_WARNING, (data) => {
      console.log('Drawdown warning:', data);
      const warningType = data.type === 'overall' ? 'Overall' : 'Daily';
      toast.warning(`${warningType} Drawdown Warning`, {
        description: `Your ${data.type} drawdown is at ${data.currentDrawdown.toFixed(2)}%. Maximum allowed is ${data.maxDrawdown}%.`,
      });
      this.emitEvent(SocketEvent.DRAWDOWN_WARNING, data);
    });

    // Trade events
    this.socket.on(SocketEvent.TRADE_CREATED, (trade) => {
      console.log('Trade created:', trade);
      this.emitEvent(SocketEvent.TRADE_CREATED, trade);
    });

    // Leaderboard events
    this.socket.on(SocketEvent.LEADERBOARD_UPDATED, (leaderboard) => {
      console.log('Leaderboard updated:', leaderboard);
      this.emitEvent(SocketEvent.LEADERBOARD_UPDATED, leaderboard);
    });

    // User metrics events
    this.socket.on(SocketEvent.USER_METRICS_UPDATED, (metrics) => {
      console.log('User metrics updated:', metrics);
      this.emitEvent(SocketEvent.USER_METRICS_UPDATED, metrics);
    });
  }

  /**
   * Emit an event to the server
   * @param event - Event name
   * @param data - Event data
   */
  public emit(event: string, data: any): void {
    if (!this.socket) {
      console.error('Socket not initialized');
      return;
    }

    this.socket.emit(event, data);
  }

  /**
   * Register an event handler
   * @param event - Event name
   * @param handler - Event handler function
   */
  public on(event: string, handler: (data: any) => void): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set());
    }

    this.eventHandlers.get(event)?.add(handler);
  }

  /**
   * Remove an event handler
   * @param event - Event name
   * @param handler - Event handler function
   */
  public off(event: string, handler: (data: any) => void): void {
    if (!this.eventHandlers.has(event)) {
      return;
    }

    this.eventHandlers.get(event)?.delete(handler);
  }

  /**
   * Emit an event to all registered handlers
   * @param event - Event name
   * @param data - Event data
   */
  private emitEvent(event: string, data: any): void {
    if (!this.eventHandlers.has(event)) {
      return;
    }

    this.eventHandlers.get(event)?.forEach((handler) => {
      handler(data);
    });
  }

  /**
   * Disconnect the socket
   */
  public disconnect(): void {
    if (!this.socket) {
      return;
    }

    this.socket.disconnect();
    this.socket = null;
    this.token = null;
    this.eventHandlers.clear();
  }

  /**
   * Check if the socket is connected
   * @returns True if the socket is connected, false otherwise
   */
  public isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * Reconnect the socket
   */
  public reconnect(): void {
    if (this.socket) {
      this.socket.connect();
    } else if (this.token) {
      this.init(this.token);
    }
  }

  /**
   * Mark a notification as read
   * @param notificationId - Notification ID
   */
  public markNotificationAsRead(notificationId: number): void {
    this.emit(SocketEvent.NOTIFICATION_READ, notificationId);
  }
}

// Export singleton instance
export const socketService = new SocketService();
