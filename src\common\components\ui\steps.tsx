import * as React from "react"
import { cn } from "@/common/utils"

/**
 * Steps component for displaying a sequence of steps
 * 
 * A component for displaying a sequence of steps in a process.
 * 
 * @component
 * @status stable
 * @version 1.0.0
 * 
 * @example
 * // Basic usage
 * <Steps>
 *   <Step>Step 1</Step>
 *   <Step>Step 2</Step>
 *   <Step>Step 3</Step>
 * </Steps>
 */
export interface StepsProps extends React.HTMLAttributes<HTMLDivElement> {
  vertical?: boolean;
}

const Steps = React.forwardRef<
  HTMLDivElement,
  StepsProps
>(({ className, vertical = false, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex",
      vertical ? "flex-col space-y-4" : "space-x-4",
      className
    )}
    {...props}
  />
))
Steps.displayName = "Steps"

/**
 * Step component
 * @component
 */
export interface StepProps extends React.HTMLAttributes<HTMLDivElement> {
  active?: boolean;
  completed?: boolean;
}

const Step = React.forwardRef<
  HTMLDivElement,
  StepProps
>(({ className, active = false, completed = false, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex items-center",
      className
    )}
    {...props}
  />
))
Step.displayName = "Step"

export { Steps, Step }
