import { cn } from "@/common/utils";

/**
 * Skeleton component for loading states
 * 
 * A component that displays a placeholder while content is loading.
 * 
 * @component
 * @status stable
 * @version 1.0.0
 * 
 * @example
 * // Basic usage
 * <Skeleton className="h-8 w-32" />
 * 
 * @example
 * // Card skeleton
 * <div className="space-y-2">
 *   <Skeleton className="h-12 w-12 rounded-full" />
 *   <div className="space-y-2">
 *     <Skeleton className="h-4 w-[250px]" />
 *     <Skeleton className="h-4 w-[200px]" />
 *   </div>
 * </div>
 */
function Skeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn("animate-pulse rounded-md bg-forex-hover", className)}
      {...props}
    />
  )
}

export { Skeleton };
