{"name": "tradechampionx-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@fontsource/inter": "^5.2.5", "@fontsource/jetbrains-mono": "^5.2.5", "@fontsource/manrope": "^5.2.5", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-tooltip": "^1.0.7", "@rainbow-me/rainbowkit": "^2.2.8", "@reduxjs/toolkit": "^2.8.1", "@tanstack/react-query": "^5.85.9", "axios": "^1.9.0", "bootstrap": "^5.3.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^10.18.0", "lightweight-charts": "^5.0.6", "lucide-react": "^0.309.0", "react": "^18.2.0", "react-day-picker": "^9.6.7", "react-dom": "^18.2.0", "react-hook-form": "^7.56.4", "react-redux": "^9.2.0", "react-router-dom": "^6.21.3", "recharts": "^2.15.3", "siwe": "^3.0.0", "socket.io-client": "^4.8.1", "sonner": "^1.3.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "viem": "^2.37.1", "wagmi": "^2.16.9", "zod": "^3.24.4", "zustand": "^5.0.8"}, "devDependencies": {"@types/node": "^20.11.5", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^6.3.4"}}