/**
 * Common types used throughout the application
 */

/**
 * Challenge type
 */
export interface Challenge {
  id: number;
  type: string;
  status: 'upcoming' | 'active' | 'completed';
  startDate: string;
  endDate: string | null;
  prizePool: number;
  entryFee: number;
  participantCount: number;
  maxParticipants: number;
  rules: ChallengeRules;
}

/**
 * Challenge rules
 */
export interface ChallengeRules {
  maxDrawdownPercent: number;
  maxRiskPerTradePercent: number;
  minTradingDays: number;
  minTradesPerDay: number;
}

/**
 * Challenge entry
 */
export interface ChallengeEntry {
  id: number;
  userId: string;
  challenge: Challenge;
  cTraderAccountId: string | null;
  cTraderAccountConnected: boolean;
  balanceVerified: boolean;
  disqualified: boolean;
  disqualificationReason: string | null;
  leaderboardPosition: number | null;
  createdAt: string;
  updatedAt: string;
}

/**
 * Challenge metrics
 */
export interface ChallengeMetrics {
  entryId: number;
  pnlAmount: number;
  pnlPercent: number;
  maxDrawdownPercent: number;
  avgRiskPerTradePercent: number;
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  avgWinAmount: number;
  avgLossAmount: number;
  profitFactor: number;
  sharpeRatio: number;
  lastUpdated: string;
}

/**
 * Trade
 */
export interface Trade {
  id: number;
  challengeEntryId: number;
  symbol: string;
  direction: 'buy' | 'sell';
  openTime: string;
  closeTime: string | null;
  openPrice: number;
  closePrice: number | null;
  volume: number;
  pnlAmount: number;
  pnlPercent: number;
  status: 'open' | 'closed';
}

/**
 * Wallet transaction
 */
export interface WalletTransaction {
  id: number;
  userId: string;
  amount: number;
  type: 'credit' | 'debit';
  reason: string;
  challengeId: number | null;
  expiryDate: string | null;
  createdAt: string;
}

/**
 * User profile
 */
export interface UserProfile {
  userId: string;
  displayName: string;
  email: string;
  country: string | null;
  avatarUrl: string | null;
  walletBalance: number;
  totalChallengesCompleted: number;
  totalEarnings: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Leaderboard entry
 */
export interface LeaderboardEntry {
  position: number;
  userId: string;
  displayName: string;
  avatarUrl: string | null;
  pnlPercent: number;
  maxDrawdownPercent: number;
  totalTrades: number;
  winRate: number;
}

/**
 * Connected account
 */
export interface ConnectedAccount {
  id: number;
  userId: string;
  cTraderAccountId: string;
  accountName: string;
  broker: string;
  balance: number;
  currency: string;
  lastUsed: string | null;
  createdAt: string;
  updatedAt: string;
}

/**
 * Notification
 */
export interface Notification {
  id: number;
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  read: boolean;
  createdAt: string;
}
