import * as React from "react";
import * as ProgressPrimitive from "@radix-ui/react-progress";
import { cn } from "@/common/utils";

/**
 * Progress component
 *
 * A progress bar component that displays a visual indicator of completion.
 *
 * @example
 * ```tsx
 * <Progress value={33} />
 * ```
 */
const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> & {
    /**
     * The value of the progress indicator (0-100)
     */
    value?: number;
    /**
     * The color of the progress indicator
     */
    color?: string;
    /**
     * Custom class name for the indicator element
     */
    indicatorClassName?: string;
  }
>(({ className, value = 0, color, indicatorClassName, ...props }, ref) => (
  <ProgressPrimitive.Root
    ref={ref}
    className={cn(
      "relative h-2 w-full overflow-hidden rounded-full bg-forex-darker",
      className
    )}
    {...props}
  >
    <ProgressPrimitive.Indicator
      className={cn(
        "h-full w-full flex-1 transition-all",
        indicatorClassName || color || "bg-gradient-to-r from-forex-primary to-forex-accent"
      )}
      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
    />
  </ProgressPrimitive.Root>
));

Progress.displayName = ProgressPrimitive.Root.displayName;

export { Progress };
