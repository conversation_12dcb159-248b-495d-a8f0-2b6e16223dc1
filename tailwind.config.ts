import type { Config } from "tailwindcss";

const config: Config = {
  darkMode: ["class"],
  content: [
    "./index.html",
    "./public/index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      fontFamily: {
        sans: ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
        display: ['Manrope', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace'],
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        success: {
          DEFAULT: "hsl(var(--success))",
          foreground: "hsl(var(--success-foreground))",
        },
        warning: {
          DEFAULT: "hsl(var(--warning))",
          foreground: "hsl(var(--warning-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        "forex-primary": "var(--forex-primary)",
        "forex-secondary": "var(--forex-secondary)",
        "forex-accent": "var(--forex-accent)",
        "forex-dark": "var(--forex-dark)",
        "forex-darker": "var(--forex-darker)",
        "forex-card": "var(--forex-card)",
        "forex-border": "var(--forex-border)",
        "forex-light": "var(--forex-light)",
        "forex-neutral": "var(--forex-neutral)",
        "forex-hover": "var(--forex-hover)",
        "forex-success": "var(--forex-success)",
        "forex-warning": "var(--forex-warning)",
        "forex-danger": "var(--forex-danger)",
        "forex-profit": "var(--forex-profit)",
        "forex-loss": "var(--forex-loss)",
        "forex-glow": "var(--forex-glow)",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        'accordion-down': {
          from: {
            height: '0',
            opacity: '0'
          },
          to: {
            height: 'var(--radix-accordion-content-height)',
            opacity: '1'
          }
        },
        'accordion-up': {
          from: {
            height: 'var(--radix-accordion-content-height)',
            opacity: '1'
          },
          to: {
            height: '0',
            opacity: '0'
          }
        },
        'slideUp': {
          '0%': {
            transform: 'translateY(20px)',
            opacity: '0'
          },
          '100%': {
            transform: 'translateY(0)',
            opacity: '1'
          }
        },
        'fadeIn': {
          '0%': {
            opacity: '0'
          },
          '100%': {
            opacity: '1'
          }
        },
        'fade-in': {
          '0%': {
            opacity: '0',
            transform: 'translateY(10px)'
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)'
          }
        },
        'fade-out': {
          '0%': {
            opacity: '1',
            transform: 'translateY(0)'
          },
          '100%': {
            opacity: '0',
            transform: 'translateY(10px)'
          }
        },
        'float': {
          '0%, 100%': {
            transform: 'translateY(0)'
          },
          '50%': {
            transform: 'translateY(-5px)'
          }
        },
        'pulse-slow': {
          '0%, 100%': {
            opacity: '1'
          },
          '50%': {
            opacity: '0.8'
          }
        },
        'gradient-x': {
          '0%, 100%': {
            'background-position': '0% 50%'
          },
          '50%': {
            'background-position': '100% 50%'
          }
        },
        'slide-in-right': {
          '0%': {
            transform: 'translateX(100%)',
            opacity: '0'
          },
          '100%': {
            transform: 'translateX(0)',
            opacity: '1'
          }
        },
        'slide-in-left': {
          '0%': {
            transform: 'translateX(-100%)',
            opacity: '0'
          },
          '100%': {
            transform: 'translateX(0)',
            opacity: '1'
          }
        },
        'scale-in': {
          '0%': {
            transform: 'scale(0.95)',
            opacity: '0'
          },
          '100%': {
            transform: 'scale(1)',
            opacity: '1'
          }
        },
        'bounce-soft': {
          '0%, 100%': {
            transform: 'translateY(0)',
            'animation-timing-function': 'cubic-bezier(0.8, 0, 1, 1)'
          },
          '50%': {
            transform: 'translateY(-5px)',
            'animation-timing-function': 'cubic-bezier(0, 0, 0.2, 1)'
          }
        },
        'glow-pulse': {
          '0%, 100%': {
            'box-shadow': '0 0 15px 2px var(--forex-glow)'
          },
          '50%': {
            'box-shadow': '0 0 25px 5px var(--forex-glow)'
          }
        },
        'shimmer': {
          '0%': {
            'background-position': '-500px 0'
          },
          '100%': {
            'background-position': '500px 0'
          }
        },
        'number-change': {
          '0%': {
            transform: 'translateY(100%)',
            opacity: '0'
          },
          '100%': {
            transform: 'translateY(0)',
            opacity: '1'
          }
        },
        'border-glow': {
          '0%, 100%': {
            'border-color': 'rgba(0, 102, 255, 0.3)'
          },
          '50%': {
            'border-color': 'rgba(0, 102, 255, 0.8)'
          }
        }
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'slideUp': 'slideUp 0.5s ease-out forwards',
        'fadeIn': 'fadeIn 0.4s ease-out forwards',
        'fade-in': 'fade-in 0.5s ease-out',
        'fade-out': 'fade-out 0.5s ease-out',
        'float': 'float 3s ease-in-out infinite',
        'pulse-slow': 'pulse-slow 3s infinite',
        'gradient-x': 'gradient-x 15s ease infinite',
        'slide-in-right': 'slide-in-right 0.5s ease-out',
        'slide-in-left': 'slide-in-left 0.5s ease-out',
        'scale-in': 'scale-in 0.3s ease-out',
        'bounce-soft': 'bounce-soft 2s ease-in-out infinite',
        'spin-slow': 'spin 8s linear infinite',
        'glow-pulse': 'glow-pulse 2s ease-in-out infinite',
        'shimmer': 'shimmer 2s linear infinite',
        'number-change': 'number-change 0.5s ease-out',
        'border-glow': 'border-glow 2s ease-in-out infinite',
      },
      backgroundImage: {
        'hero-pattern': 'linear-gradient(to bottom right, rgba(0, 0, 0, 0.95), rgba(5, 5, 5, 0.98)), url("/bg-pattern.svg")',
        'gradient-primary': 'linear-gradient(to right, #0066FF, #00A3FF)',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, #0066FF, #00A3FF)',
        'glass-card': 'linear-gradient(to right bottom, rgba(10, 10, 10, 0.8), rgba(5, 5, 5, 0.9))',
        'shimmer-gradient': 'linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgba(0, 102, 255, 0.2) 20%, rgba(0, 102, 255, 0.3) 40%, rgba(0, 0, 0, 0) 60%)',
        'premium-dark': 'radial-gradient(circle at center, rgba(10, 10, 10, 0.8) 0%, rgba(0, 0, 0, 1) 100%)',
        'premium-card': 'linear-gradient(135deg, rgba(15, 15, 15, 0.9) 0%, rgba(5, 5, 5, 0.95) 100%)',
        'premium-glow': 'linear-gradient(to right, rgba(0, 102, 255, 0.1), rgba(0, 163, 255, 0.1))',
      },
      transitionProperty: {
        'height': 'height',
        'spacing': 'margin, padding',
      },
      boxShadow: {
        'glow-primary': '0 0 15px rgba(0, 102, 255, 0.5)',
        'glow-secondary': '0 0 15px rgba(0, 200, 83, 0.5)',
        'glass': '0 8px 32px 0 rgba(0, 0, 0, 0.5)',
        'premium-card': '0 10px 30px -5px rgba(0, 0, 0, 0.8), 0 0 5px 0 rgba(0, 102, 255, 0.2)',
        'premium-blue': '0 0 20px 0 rgba(0, 102, 255, 0.5)',
        'premium-green': '0 0 20px 0 rgba(0, 200, 83, 0.5)',
        'premium-red': '0 0 20px 0 rgba(255, 61, 0, 0.5)',
        'inner-glow': 'inset 0 0 10px 0 rgba(0, 102, 255, 0.2)',
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    function({ addUtilities }) {
      const newUtilities = {
        '.text-shadow-sm': {
          'text-shadow': '0 1px 2px rgba(0, 0, 0, 0.2)'
        },
        '.text-shadow': {
          'text-shadow': '0 2px 4px rgba(0, 0, 0, 0.3)'
        },
        '.text-shadow-lg': {
          'text-shadow': '0 4px 8px rgba(0, 0, 0, 0.4)'
        },
        '.text-glow-blue': {
          'text-shadow': '0 0 10px rgba(0, 102, 255, 0.7)'
        },
        '.text-glow-green': {
          'text-shadow': '0 0 10px rgba(0, 200, 83, 0.7)'
        },
        '.text-glow-red': {
          'text-shadow': '0 0 10px rgba(255, 61, 0, 0.7)'
        },
        '.glass': {
          'background': 'rgba(255, 255, 255, 0.15)',
          'backdrop-filter': 'blur(10px)',
          'border': '1px solid rgba(255, 255, 255, 0.125)',
        },
        '.glass-dark': {
          'background': 'rgba(10, 10, 10, 0.7)',
          'backdrop-filter': 'blur(10px)',
          'border': '1px solid rgba(26, 26, 26, 0.5)',
        },
        '.glass-premium': {
          'background': 'rgba(5, 5, 5, 0.8)',
          'backdrop-filter': 'blur(15px)',
          'border': '1px solid rgba(0, 102, 255, 0.2)',
          'box-shadow': '0 10px 30px -5px rgba(0, 0, 0, 0.8), 0 0 5px 0 rgba(0, 102, 255, 0.2)',
        },
        '.premium-border': {
          'border': '1px solid rgba(0, 102, 255, 0.3)',
          'box-shadow': '0 0 5px 0 rgba(0, 102, 255, 0.2)',
        },
        '.premium-border-glow': {
          'border': '1px solid rgba(0, 102, 255, 0.5)',
          'box-shadow': '0 0 10px 0 rgba(0, 102, 255, 0.3)',
          'animation': 'border-glow 2s ease-in-out infinite',
        },
        '.premium-card': {
          'background': 'linear-gradient(135deg, rgba(15, 15, 15, 0.9) 0%, rgba(5, 5, 5, 0.95) 100%)',
          'border': '1px solid rgba(26, 26, 26, 0.8)',
          'box-shadow': '0 10px 30px -5px rgba(0, 0, 0, 0.8), 0 0 5px 0 rgba(0, 102, 255, 0.1)',
        },
        '.scrollbar-hide': {
          '-ms-overflow-style': 'none',
          'scrollbar-width': 'none',
          '&::-webkit-scrollbar': {
            display: 'none',
          },
        },
        '.shimmer': {
          'background': 'linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgba(0, 102, 255, 0.2) 20%, rgba(0, 102, 255, 0.3) 40%, rgba(0, 0, 0, 0) 60%)',
          'background-size': '1000px 100%',
          'animation': 'shimmer 2s linear infinite',
        },
      }
      addUtilities(newUtilities)
    }
  ],
  safelist: [
    'bg-forex-primary',
    'bg-forex-secondary',
    'bg-forex-accent',
    'hover:bg-forex-primary/90',
    'hover:bg-forex-secondary/90',
    'hover:bg-forex-accent/90',
    'text-forex-primary',
    'text-forex-secondary',
    'text-forex-accent',
    'border-forex-primary',
    'border-forex-secondary',
    'border-forex-accent',
    'from-forex-primary',
    'from-forex-secondary',
    'from-forex-accent',
    'to-forex-primary',
    'to-forex-secondary',
    'to-forex-accent',
    'border-forex-primary/10',
    'border-forex-primary/20',
    'border-forex-secondary/10',
    'border-forex-secondary/20',
    'border-forex-accent/10',
    'border-forex-accent/20',
    'data-[state=active]:bg-forex-primary',
    'data-[state=active]:bg-forex-secondary',
    'data-[state=active]:bg-forex-accent',
    'border-t-forex-primary',
    'border-t-forex-secondary',
    'border-t-forex-accent',
    'animate-spin-slow',
    'animate-float',
    'animate-bounce-soft',
    'border-t-forex-loss',
    'border-t-forex-profit',
    'group-hover:animate-pulse',
    'group-hover:animate-float',
    'group-hover:animate-bounce-soft',
    'animate-glow-pulse',
    'animate-shimmer',
    'animate-number-change',
    'animate-border-glow',
    'bg-shimmer-gradient',
    'bg-premium-dark',
    'bg-premium-card',
    'bg-premium-glow',
    'shadow-premium-card',
    'shadow-premium-blue',
    'shadow-premium-green',
    'shadow-premium-red',
    'shadow-inner-glow',
    'text-forex-profit',
    'text-forex-loss',
    'hover:shadow-premium-blue',
    'hover:shadow-premium-green',
    'hover:shadow-premium-red',
    'hover:animate-glow-pulse',
    'hover:animate-border-glow',
    'group-hover:animate-glow-pulse',
    'group-hover:animate-shimmer',
    'group-hover:animate-border-glow',
  ]
};

export default config;
