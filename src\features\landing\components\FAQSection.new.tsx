import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/common/components/ui/accordion";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/common/components/ui/tabs";
import { Input } from "@/common/components/ui/input";
import { Badge } from "@/common/components/ui/badge";
import { Button } from "@/common/components/ui/button";
import { useState, useEffect } from "react";
import { Info, DollarSign, Trophy, Users, Shield, BarChart3, Search, X, Tag, ArrowRight } from "lucide-react";

// Simple version of FAQ categories
const faqCategories = [
  {
    id: "general",
    name: "General",
    icon: Info,
    questions: [
      {
        question: "How is TradeChampionX different from prop firms?",
        answer: "Unlike traditional prop firms that profit when traders fail, our community-focused model distributes 75% of entry fees directly to top performers. We have transparent rules, no hidden disqualification clauses, no restrictive trading windows, and guaranteed payouts with no withdrawal hoops to jump through."
      },
      {
        question: "What types of challenges do you offer?",
        answer: "We offer daily, weekly, and monthly challenges, each with different entry fees, prize pools, and rule sets. Daily challenges are perfect for quick results, weekly challenges offer a balance of time and reward, while monthly challenges provide the largest prize pools and most flexible trading conditions."
      }
    ]
  },
  {
    id: "prizes",
    name: "Prizes & Rewards",
    icon: Trophy,
    questions: [
      {
        question: "How does the dynamic prize pool work?",
        answer: "Our prize pool grows with each participant. 75% of all entry fees go directly to the community: 60% to top performers (1st, 2nd, 3rd place) and 15% to wallet credits for the top 30% of traders. This creates a truly community-driven reward system where everyone benefits from growth."
      },
      {
        question: "How are prizes distributed?",
        answer: "Prize distribution varies by challenge type. For daily challenges, 50% goes to 1st place, 30% to 2nd, and 20% to 3rd. Weekly challenges distribute 40% to 1st, 25% to 2nd, 15% to 3rd, and 20% split among 4th-10th places. Monthly challenges have a broader distribution with additional participation credits."
      }
    ]
  }
];

const FAQSection = () => {
  const [activeCategory, setActiveCategory] = useState("general");
  const [searchQuery, setSearchQuery] = useState("");

  return (
    <section className="py-20 bg-forex-light">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <span className="inline-block px-4 py-1 bg-white text-forex-primary font-medium rounded-full mb-4 shadow-sm">
            Answers to Your Questions
          </span>
          <h2 className="text-3xl md:text-4xl font-bold text-forex-dark mb-4">
            Frequently Asked Questions
          </h2>
          <p className="text-lg text-forex-neutral">
            Everything you need to know about our trading challenges
          </p>
        </div>

        {/* Search Bar */}
        <div className="max-w-2xl mx-auto mb-8">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="h-5 w-5 text-forex-neutral" />
            </div>
            <Input
              type="search"
              placeholder="Search for questions or keywords..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-10 py-3 bg-white border-forex-border/30 focus:border-forex-primary focus:ring-forex-primary/20"
            />
            {searchQuery && (
              <button
                className="absolute inset-y-0 right-0 flex items-center pr-3"
                onClick={() => setSearchQuery("")}
              >
                <X className="h-5 w-5 text-forex-neutral hover:text-forex-primary" />
              </button>
            )}
          </div>
        </div>

        <div className="max-w-5xl mx-auto">
          <Tabs defaultValue="general" value={activeCategory} onValueChange={setActiveCategory} className="w-full">
            <div className="bg-white rounded-t-xl p-4 border-b border-forex-border/30">
              <TabsList className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2">
                {faqCategories.map((category) => (
                  <TabsTrigger
                    key={category.id}
                    value={category.id}
                    className="flex items-center gap-2 py-2 data-[state=active]:bg-forex-primary data-[state=active]:text-white"
                  >
                    <category.icon className="w-4 h-4" />
                    <span className="hidden md:inline">{category.name}</span>
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>

            <div className="bg-white rounded-b-xl p-6 shadow-sm">
              {faqCategories.map((category) => (
                <TabsContent key={category.id} value={category.id} className="mt-0">
                  <div className="flex items-center gap-2 mb-6 pb-4 border-b border-forex-border/20">
                    <category.icon className="w-5 h-5 text-forex-primary" />
                    <h3 className="text-xl font-bold text-forex-dark">{category.name} Questions</h3>
                  </div>
                  
                  <Accordion type="single" collapsible className="w-full">
                    {category.questions.map((faq, index) => (
                      <AccordionItem
                        key={index}
                        value={`${category.id}-item-${index}`}
                        className="border-b border-forex-border/30"
                      >
                        <AccordionTrigger
                          id={`accordion-${category.id}-${index}`}
                          className="text-lg font-medium text-left py-5 hover:text-forex-primary"
                        >
                          {faq.question}
                        </AccordionTrigger>
                        <AccordionContent className="text-forex-neutral pb-5 pt-2">
                          {faq.answer}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </TabsContent>
              ))}

              <div className="text-center mt-12 p-6 bg-forex-light rounded-xl border border-forex-border/30">
                <h3 className="font-bold text-xl mb-3 text-forex-dark">Still have questions?</h3>
                <p className="text-forex-neutral mb-4">
                  Our support team is ready to help with any questions about our trading challenges.
                </p>
                <a
                  href="#"
                  className="inline-block px-6 py-3 bg-forex-primary text-white rounded-lg hover:bg-forex-hover transition-colors font-medium"
                >
                  Contact Support
                </a>
              </div>
            </div>
          </Tabs>
        </div>
      </div>
    </section>
  );
};

export default FAQSection;
