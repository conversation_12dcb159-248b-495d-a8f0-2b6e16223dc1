import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * Common utility functions for the application
 * 
 * This module exports various utility functions that are used throughout the application.
 * These functions provide common functionality like class name merging, date formatting,
 * and other reusable operations.
 * 
 * @module common/utils
 */

/**
 * Combines multiple class names using clsx and tailwind-merge
 *
 * This utility function combines multiple class names and resolves Tailwind CSS
 * conflicts by using tailwind-merge. It's useful for conditionally applying
 * classes and merging class names from different sources.
 *
 * @param {...ClassValue[]} inputs - Class names to combine
 * @returns {string} - Combined and merged class names
 *
 * @example
 * // Basic usage
 * <div className={cn("text-red-500", "bg-blue-500")}>
 *
 * @example
 * // With conditional classes
 * <div className={cn(
 *   "base-class",
 *   isActive && "active-class",
 *   variant === "primary" ? "primary-class" : "secondary-class"
 * )}>
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format a date into a human-readable string
 * 
 * @param {Date} date - The date to format
 * @param {Intl.DateTimeFormatOptions} options - Formatting options to pass to Intl.DateTimeFormat
 * @returns {string} Formatted date string according to the user's locale
 * 
 * @example
 * // Returns "Jan 1, 2023"
 * formatDate(new Date(2023, 0, 1))
 * 
 * @example
 * // Returns "January 1, 2023" with custom options
 * formatDate(new Date(2023, 0, 1), { month: 'long', day: 'numeric', year: 'numeric' })
 */
export function formatDate(date: Date, options?: Intl.DateTimeFormatOptions): string {
  const defaultOptions: Intl.DateTimeFormatOptions = {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    ...options
  };
  
  return new Intl.DateTimeFormat('en-US', defaultOptions).format(date);
}

/**
 * Format a number with commas for thousands separators
 * 
 * @param {number} num - The number to format
 * @returns {string} Formatted number with commas as thousands separators
 * 
 * @example
 * // Returns "1,234,567"
 * formatNumberWithCommas(1234567)
 */
export function formatNumberWithCommas(num: number): string {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}
