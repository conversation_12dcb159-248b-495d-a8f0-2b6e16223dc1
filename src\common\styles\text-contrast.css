/* Text Contrast Utility Classes */

/* For dark backgrounds */
.text-on-dark {
  color: #ffffff !important; /* White text on dark backgrounds */
}

.text-on-dark-muted {
  color: rgba(255, 255, 255, 0.8) !important; /* Slightly muted white text */
}

.text-on-dark-subtle {
  color: rgba(255, 255, 255, 0.6) !important; /* More muted white text */
}

/* For light backgrounds */
.text-on-light {
  color: #0f172a !important; /* Dark text on light backgrounds */
}

.text-on-light-muted {
  color: #475569 !important; /* Slightly muted dark text */
}

.text-on-light-subtle {
  color: #64748b !important; /* More muted dark text */
}

/* For primary color backgrounds */
.text-on-primary {
  color: #ffffff !important; /* White text on primary color */
}

/* For accent color backgrounds */
.text-on-accent {
  color: #ffffff !important; /* White text on accent color */
}

/* For card backgrounds */
.text-on-card-dark {
  color: #ffffff !important; /* White text on dark cards */
}

.text-on-card-light {
  color: #0f172a !important; /* Dark text on light cards */
}

/* Headings */
.heading-on-dark {
  color: #ffffff !important; /* White headings on dark backgrounds */
}

.heading-on-light {
  color: #0f172a !important; /* Dark headings on light backgrounds */
}

/* Links */
.link-on-dark {
  color: #38bdf8 !important; /* Light blue links on dark backgrounds */
}

.link-on-dark:hover {
  color: #7dd3fc !important; /* Lighter blue on hover */
}

.link-on-light {
  color: #0284c7 !important; /* Dark blue links on light backgrounds */
}

.link-on-light:hover {
  color: #0369a1 !important; /* Darker blue on hover */
}

/* Buttons text */
.btn-text-on-dark {
  color: #ffffff !important; /* White text for buttons on dark backgrounds */
}

.btn-text-on-light {
  color: #0f172a !important; /* Dark text for buttons on light backgrounds */
}

/* Force visibility classes */
.force-visible-text {
  color: #ffffff !important; /* White text that's always visible */
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.8) !important; /* Text shadow for better contrast */
}

.force-visible-dark-text {
  color: #0f172a !important; /* Dark text that's always visible */
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.8) !important; /* Text shadow for better contrast */
}
