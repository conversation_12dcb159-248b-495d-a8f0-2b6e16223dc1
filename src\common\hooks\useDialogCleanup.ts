/**
 * useDialogCleanup Hook
 * @description Custom hook to ensure proper dialog cleanup and prevent UI freezing
 * @version 1.0.0
 * @status stable
 */

import { useEffect, useCallback, useRef } from 'react';

interface UseDialogCleanupOptions {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  cleanupDelay?: number;
}

/**
 * Hook to ensure proper dialog cleanup and prevent UI freezing issues
 * 
 * @param options - Configuration options for dialog cleanup
 * @returns Enhanced onOpenChange handler with cleanup
 */
export const useDialogCleanup = ({
  isOpen,
  onOpenChange,
  cleanupDelay = 0
}: UseDialogCleanupOptions) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isClosingRef = useRef(false);

  // Enhanced onOpenChange handler with proper cleanup
  const handleOpenChange = useCallback((newOpen: boolean) => {
    // Clear any pending timeouts
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    if (!newOpen && !isClosingRef.current) {
      // Mark as closing to prevent multiple close attempts
      isClosingRef.current = true;

      // Force cleanup of any lingering overlays
      const cleanupOverlays = () => {
        // Remove any stuck dialog overlays
        const overlays = document.querySelectorAll('[data-radix-dialog-overlay]');
        overlays.forEach(overlay => {
          const element = overlay as HTMLElement;
          if (element.getAttribute('data-state') === 'closed') {
            element.style.display = 'none';
            element.style.pointerEvents = 'none';
          }
        });

        // Remove any stuck dialog content
        const contents = document.querySelectorAll('[data-radix-dialog-content]');
        contents.forEach(content => {
          const element = content as HTMLElement;
          if (element.getAttribute('data-state') === 'closed') {
            element.style.display = 'none';
            element.style.pointerEvents = 'none';
          }
        });

        // Ensure body scroll is restored
        document.body.style.overflow = '';
        document.body.removeAttribute('data-scroll-locked');

        // Reset closing flag
        isClosingRef.current = false;
      };

      if (cleanupDelay > 0) {
        // Delayed cleanup for animations
        timeoutRef.current = setTimeout(() => {
          onOpenChange(false);
          setTimeout(cleanupOverlays, 50);
        }, cleanupDelay);
      } else {
        // Immediate cleanup
        setTimeout(() => {
          onOpenChange(false);
          cleanupOverlays();
        }, 0);
      }
    } else if (newOpen) {
      // Reset closing flag when opening
      isClosingRef.current = false;
      onOpenChange(true);
    }
  }, [onOpenChange, cleanupDelay]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Force cleanup if component unmounts while dialog is open
      if (isOpen) {
        const overlays = document.querySelectorAll('[data-radix-dialog-overlay]');
        overlays.forEach(overlay => {
          const element = overlay as HTMLElement;
          element.style.display = 'none';
          element.style.pointerEvents = 'none';
        });

        const contents = document.querySelectorAll('[data-radix-dialog-content]');
        contents.forEach(content => {
          const element = content as HTMLElement;
          element.style.display = 'none';
          element.style.pointerEvents = 'none';
        });

        // Restore body scroll
        document.body.style.overflow = '';
        document.body.removeAttribute('data-scroll-locked');
      }
    };
  }, [isOpen]);

  // Force cleanup if dialog state becomes inconsistent
  useEffect(() => {
    if (!isOpen && !isClosingRef.current) {
      const checkForStuckOverlays = () => {
        const overlays = document.querySelectorAll('[data-radix-dialog-overlay][data-state="open"]');
        const contents = document.querySelectorAll('[data-radix-dialog-content][data-state="open"]');
        
        if (overlays.length > 0 || contents.length > 0) {
          console.warn('Detected stuck dialog elements, forcing cleanup');
          
          overlays.forEach(overlay => {
            const element = overlay as HTMLElement;
            element.style.display = 'none';
            element.style.pointerEvents = 'none';
            element.setAttribute('data-state', 'closed');
          });

          contents.forEach(content => {
            const element = content as HTMLElement;
            element.style.display = 'none';
            element.style.pointerEvents = 'none';
            element.setAttribute('data-state', 'closed');
          });

          // Restore body scroll
          document.body.style.overflow = '';
          document.body.removeAttribute('data-scroll-locked');
        }
      };

      // Check for stuck overlays after a short delay
      setTimeout(checkForStuckOverlays, 100);
    }
  }, [isOpen]);

  return {
    handleOpenChange,
    isClosing: isClosingRef.current
  };
};

/**
 * Simple version of the hook for basic cleanup
 */
export const useSimpleDialogCleanup = (onOpenChange: (open: boolean) => void) => {
  return useCallback((newOpen: boolean) => {
    if (!newOpen) {
      // Ensure proper cleanup when closing
      setTimeout(() => {
        onOpenChange(false);
        
        // Force cleanup of any lingering elements
        const overlays = document.querySelectorAll('[data-radix-dialog-overlay][data-state="closed"]');
        overlays.forEach(overlay => {
          const element = overlay as HTMLElement;
          element.style.display = 'none';
        });
      }, 0);
    } else {
      onOpenChange(newOpen);
    }
  }, [onOpenChange]);
};

export default useDialogCleanup;
