import React, { useState, useEffect } from 'react';
import { X, Wallet, Shield, Zap, CheckCircle, AlertCircle, Loader2, RefreshCw } from 'lucide-react';
import { Button } from './button';
import { useAccount, useConnect, useDisconnect } from 'wagmi';
import { injected } from 'wagmi/connectors';

interface WalletModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface WalletOption {
  id: string;
  name: string;
  icon: string;
  connector: any;
  description: string;
}

export const WalletModal: React.FC<WalletModalProps> = ({ isOpen, onClose }) => {
  // Mock auth state - replace with real auth hooks when available
  const isAuthenticated = false;
  const logout = () => console.log('logout');
  const signIn = async () => console.log('signIn');
  const siweLoading = false;

  const { address, isConnected } = useAccount();
  const { connect, isPending: isConnecting } = useConnect();
  const { disconnect } = useDisconnect();
  const [step, setStep] = useState<'connect' | 'sign' | 'success' | 'connected'>('connect');
  const [error, setError] = useState<string | null>(null);
  const [selectedWallet, setSelectedWallet] = useState<string | null>(null);
  const [isChangingWallet, setIsChangingWallet] = useState(false);

  // Available wallet options
  const walletOptions: WalletOption[] = [
    {
      id: 'metamask',
      name: 'MetaMask',
      icon: '🦊',
      connector: injected(),
      description: 'Most popular Ethereum wallet'
    },
    {
      id: 'injected',
      name: 'Browser Wallet',
      icon: '🌐',
      connector: injected(),
      description: 'Use any injected wallet (Brave, etc.)'
    }
  ];

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      if (isAuthenticated) {
        setStep('connected');
      } else if (isConnected && address && !isChangingWallet) {
        setStep('sign');
      } else {
        setStep('connect');
      }
      setError(null);
      setSelectedWallet(null);
    } else {
      // Reset changing wallet flag when modal closes
      setIsChangingWallet(false);
    }
  }, [isOpen, isAuthenticated, isConnected, address, isChangingWallet]);

  // Handle wallet connection
  const handleConnect = async (walletOption: WalletOption) => {
    try {
      setError(null);
      setSelectedWallet(walletOption.id);
      setIsChangingWallet(false); // Reset the flag when connecting

      // If already connected to a different wallet, disconnect first
      if (isConnected) {
        disconnect();
        // Wait a bit for disconnection
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      connect({ connector: walletOption.connector });
    } catch (err) {
      console.error('Wallet connection error:', err);
      setError(`Failed to connect ${walletOption.name}. Please try again.`);
      setSelectedWallet(null);
    }
  };

  // Handle disconnect
  const handleDisconnect = () => {
    disconnect();
    logout();
    setStep('connect');
    setSelectedWallet(null);
    setError(null);
  };

  // Force wallet selection (disconnect current and show options)
  const handleChangeWallet = () => {
    setIsChangingWallet(true); // Set flag to prevent auto-progression
    if (isConnected) {
      disconnect();
    }
    setStep('connect');
    setSelectedWallet(null);
    setError(null);
  };

  // Handle authentication
  const handleAuthenticate = async () => {
    if (!address) return;

    try {
      setError(null);
      const success = await signIn(address);
      if (success) {
        setStep('success');
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        setError('Authentication failed. Please try again.');
      }
    } catch (err) {
      setError('Authentication failed. Please try again.');
    }
  };

  // Auto-proceed to sign step when wallet connects (unless user is changing wallets)
  useEffect(() => {
    if (isConnected && address && step === 'connect' && !isChangingWallet) {
      setStep('sign');
    }
  }, [isConnected, address, step, isChangingWallet]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center animate-fade-in">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm transition-opacity duration-300"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative bg-forex-dark border border-forex-border/30 rounded-2xl shadow-2xl max-w-md w-full mx-4 animate-modal-in hover-lift">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-forex-border/20">
          <h2 className="text-xl font-semibold text-white">Connect Wallet</h2>
          <button
            onClick={onClose}
            className="text-white/60 hover:text-white transition-colors p-1"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'connect' && (
            <div className="space-y-6 animate-fade-in">
              <div className="text-center">
                <div className="w-16 h-16 bg-forex-primary/10 rounded-full flex items-center justify-center mx-auto mb-4 animate-bounce-gentle hover-scale">
                  <Wallet className="w-8 h-8 text-forex-primary animate-pulse-slow" />
                </div>
                <h3 className="text-lg font-medium text-white mb-2">
                  {isConnected ? 'Switch Wallet' : 'Connect Your Wallet'}
                </h3>
                <p className="text-white/60 text-sm">
                  {isConnected
                    ? 'Choose a different wallet to connect with'
                    : 'Connect your wallet to start trading and access exclusive features'
                  }
                </p>
              </div>

              {/* Current Wallet Status */}
              {isConnected && address && (
                <div className="bg-green-500/10 border border-green-500/20 rounded-xl p-3 mb-4">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="w-5 h-5 text-green-400" />
                    <div>
                      <p className="text-sm font-medium text-green-400">Currently Connected</p>
                      <p className="text-xs text-white/60">{address.slice(0, 6)}...{address.slice(-4)}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Wallet Options */}
              <div className="space-y-3">
                {walletOptions.map((wallet) => (
                  <Button
                    key={wallet.id}
                    onClick={() => handleConnect(wallet)}
                    disabled={isConnecting}
                    className={`
                      w-full text-white font-medium py-4 rounded-xl transition-all duration-200 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed hover-lift
                      ${selectedWallet === wallet.id
                        ? 'bg-gradient-to-r from-forex-primary to-forex-hover gradient-animate'
                        : 'bg-forex-card/50 hover:bg-forex-card/80 border border-forex-border/30'
                      }
                    `}
                  >
                    {isConnecting && selectedWallet === wallet.id ? (
                      <div className="flex items-center justify-center space-x-3">
                        <Loader2 className="w-5 h-5 animate-spin" />
                        <span>Connecting to {wallet.name}...</span>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <span className="text-2xl">{wallet.icon}</span>
                          <div className="text-left">
                            <div className="font-semibold">{wallet.name}</div>
                            <div className="text-xs text-white/60">{wallet.description}</div>
                          </div>
                        </div>
                        <Wallet className="w-5 h-5 text-white/60" />
                      </div>
                    )}
                  </Button>
                ))}

                {/* Change Wallet Option */}
                {isConnected && (
                  <div className="pt-3 border-t border-forex-border/20">
                    <Button
                      onClick={handleDisconnect}
                      variant="outline"
                      className="w-full border-forex-border/30 text-white/80 hover:bg-forex-primary/10 hover:border-forex-primary/50"
                    >
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Switch Wallet
                    </Button>
                  </div>
                )}
              </div>

              {/* Features */}
              <div className="space-y-3 pt-4 border-t border-forex-border/20">
                <div className="flex items-center space-x-3 text-sm">
                  <Shield className="w-4 h-4 text-forex-primary" />
                  <span className="text-white/80">Secure & encrypted connection</span>
                </div>
                <div className="flex items-center space-x-3 text-sm">
                  <Zap className="w-4 h-4 text-forex-primary" />
                  <span className="text-white/80">Lightning fast transactions</span>
                </div>
              </div>
            </div>
          )}

          {step === 'sign' && (
            <div className="space-y-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-forex-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-8 h-8 text-forex-primary" />
                </div>
                <h3 className="text-lg font-medium text-white mb-2">Sign Message</h3>
                <p className="text-white/60 text-sm">
                  Sign a message to verify your wallet ownership
                </p>
              </div>

              <div className="bg-forex-card/30 rounded-xl p-4 border border-forex-border/20">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                      <CheckCircle className="w-4 h-4 text-green-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-white">Wallet Connected</p>
                      <p className="text-xs text-white/60">{address?.slice(0, 6)}...{address?.slice(-4)}</p>
                    </div>
                  </div>
                  <Button
                    onClick={handleChangeWallet}
                    variant="outline"
                    size="sm"
                    className="border-forex-border/30 text-white/80 hover:bg-forex-primary/10 hover:border-forex-primary/50"
                  >
                    <RefreshCw className="w-3 h-3 mr-1" />
                    Change
                  </Button>
                </div>
              </div>

              <div className="space-y-3">
                <Button
                  onClick={handleAuthenticate}
                  disabled={siweLoading}
                  className="w-full bg-gradient-to-r from-forex-primary to-forex-hover hover:from-forex-hover hover:to-forex-primary text-white font-medium py-3 rounded-xl transition-all duration-200 transform hover:scale-[1.02] disabled:opacity-50"
                >
                  {siweLoading ? (
                    <div className="flex items-center justify-center space-x-2">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>Signing...</span>
                    </div>
                  ) : (
                    'Sign Message'
                  )}
                </Button>

                <Button
                  onClick={handleChangeWallet}
                  variant="outline"
                  className="w-full border-forex-border/30 text-white/80 hover:bg-forex-primary/10 hover:border-forex-primary/50"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Use Different Wallet
                </Button>
              </div>
            </div>
          )}

          {step === 'success' && (
            <div className="space-y-6 animate-fade-in">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-4 success-glow animate-bounce-gentle">
                  <CheckCircle className="w-8 h-8 text-green-400 animate-pulse-slow" />
                </div>
                <h3 className="text-lg font-medium text-white mb-2">Welcome!</h3>
                <p className="text-white/60 text-sm">
                  Your wallet has been connected successfully
                </p>
              </div>

              <div className="bg-green-500/10 rounded-xl p-4 border border-green-500/20 animate-glow">
                <div className="text-center">
                  <p className="text-sm font-medium text-green-400">Authentication Complete</p>
                  <p className="text-xs text-white/60 mt-1 animate-pulse">Redirecting...</p>
                </div>
              </div>
            </div>
          )}

          {step === 'connected' && (
            <div className="space-y-6 animate-fade-in">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-4 success-glow">
                  <CheckCircle className="w-8 h-8 text-green-400" />
                </div>
                <h3 className="text-lg font-medium text-white mb-2">Wallet Connected</h3>
                <p className="text-white/60 text-sm">
                  Your wallet is connected and authenticated
                </p>
              </div>

              <div className="bg-forex-card/30 rounded-xl p-4 border border-forex-border/20">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center">
                      <CheckCircle className="w-5 h-5 text-green-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-white">Connected</p>
                      <p className="text-xs text-white/60">{address?.slice(0, 6)}...{address?.slice(-4)}</p>
                    </div>
                  </div>
                  <Button
                    onClick={handleDisconnect}
                    variant="outline"
                    size="sm"
                    className="border-red-500/30 text-red-400 hover:bg-red-500/10 hover:border-red-500/50"
                  >
                    Disconnect
                  </Button>
                </div>
              </div>

              <div className="space-y-3">
                <Button
                  onClick={onClose}
                  className="w-full bg-gradient-to-r from-forex-primary to-forex-hover hover:from-forex-hover hover:to-forex-primary text-white font-medium py-3 rounded-xl transition-all duration-200 transform hover:scale-[1.02]"
                >
                  Continue Trading
                </Button>

                <Button
                  onClick={() => setStep('connect')}
                  variant="outline"
                  className="w-full border-forex-border/30 text-white/80 hover:bg-forex-primary/10 hover:border-forex-primary/50"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Switch Wallet
                </Button>
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="mt-4 bg-red-500/10 border border-red-500/20 rounded-xl p-3">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-4 h-4 text-red-400" />
                <p className="text-sm text-red-400">{error}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
