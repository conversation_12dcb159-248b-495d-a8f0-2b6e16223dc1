/* Mobile Optimizations CSS */

/* Improve tap targets for mobile */
@media (max-width: 768px) {
  /* Increase tap target size for interactive elements */
  button,
  a,
  input[type="button"],
  input[type="submit"],
  input[type="reset"],
  input[type="checkbox"],
  input[type="radio"],
  select,
  .interactive {
    min-height: 44px;
    min-width: 44px;
  }

  /* Increase font size for better readability */
  body {
    font-size: 16px;
  }

  /* Improve form elements */
  input,
  select,
  textarea {
    font-size: 16px; /* Prevents iOS zoom on focus */
  }

  /* Prevent text from being too small */
  .text-xs {
    font-size: 0.8125rem !important; /* 13px */
  }

  /* Improve spacing for mobile */
  .container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Improve scrolling */
  .scrollable {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Prevent fixed elements from jumping during scroll on iOS */
  .fixed {
    transform: translateZ(0);
  }

  /* Improve touch feedback */
  .touch-feedback:active {
    opacity: 0.7;
    transform: scale(0.98);
  }

  /* Prevent text selection on interactive elements */
  .no-select,
  button,
  a {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  /* Improve mobile tables */
  table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  /* Improve mobile modals */
  .modal-mobile {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    border-radius: 0 !important;
  }

  /* Improve mobile navigation */
  .nav-mobile {
    padding: 0.75rem !important;
  }

  /* Improve mobile cards */
  .card-mobile {
    margin-left: -1rem !important;
    margin-right: -1rem !important;
    border-radius: 0 !important;
  }

  /* Improve mobile buttons */
  .btn-mobile {
    width: 100% !important;
    justify-content: center !important;
  }

  /* Improve mobile dropdowns */
  .dropdown-mobile {
    width: 100% !important;
    left: 0 !important;
    right: 0 !important;
  }

  /* Improve mobile tooltips */
  .tooltip-mobile {
    max-width: 90vw !important;
  }

  /* Improve mobile scrollbars */
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  /* Improve mobile focus outlines */
  :focus {
    outline: 2px solid rgba(59, 130, 246, 0.5) !important;
  }

  /* Improve mobile touch action */
  * {
    touch-action: manipulation;
  }
}

/* Landscape orientation optimizations */
@media (max-width: 768px) and (orientation: landscape) {
  /* Adjust fixed elements for landscape */
  .fixed-landscape {
    position: absolute !important;
  }

  /* Adjust height for landscape */
  .h-screen {
    height: 100% !important;
  }
}

/* High pixel density screens */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Sharper borders for high DPI screens */
  .border-hairline {
    border-width: 0.5px !important;
  }
}

/* Prevent pull-to-refresh on iOS */
html, body {
  overscroll-behavior-y: contain;
}

/* Improve mobile performance */
@media (max-width: 768px) {
  /* Reduce animation complexity on mobile */
  .animate-mobile {
    animation-duration: 0.2s !important;
    transition-duration: 0.2s !important;
  }

  /* Disable hover effects on touch devices */
  @media (hover: none) {
    .hover\:* {
      display: none !important;
    }
  }
}

/* Utility classes for mobile-specific behaviors */
.mobile-only {
  display: none !important;
}

.desktop-only {
  display: block !important;
}

@media (max-width: 768px) {
  .mobile-only {
    display: block !important;
  }

  .desktop-only {
    display: none !important;
  }
}
