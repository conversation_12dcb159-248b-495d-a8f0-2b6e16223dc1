/**
 * Dashboard Service
 * @description Consolidated service for dashboard data to reduce multiple API calls
 * @version 1.0.0
 */

import { apiService } from './api';

export interface DashboardData {
  user: {
    id: string;
    username: string;
    email: string;
    walletCredit: number;
    status: string;
  };
  summary: {
    totalChallengeEntries: number;
    activeChallengeEntries: number;
    totalTrades: number;
    totalPnl: number;
    unreadNotifications: number;
  };
  challengeEntries: Array<{
    id: number;
    challengeId: number;
    challengeName: string;
    challengeType: string;
    connectStatus: string;
    disqualified: boolean;
    enrollmentTime: string;
    recentTrades: any[];
    currentEquity: number;
  }>;
  activeChallenges: Array<{
    id: number;
    name: string;
    type: string;
    entryFee: number;
    prizePool: number;
    startDate: string;
    endDate: string;
    currentParticipants: number;
    maxParticipants: number | null;
  }>;
  recentActivity: Array<{
    id: number;
    symbol: string;
    pnl: number;
    entryTime: string;
    exitTime: string;
    challengeName: string;
    challengeType: string;
  }>;
  walletTransactions: any[];
  notifications: any[];
  metrics: any;
}

export interface ChallengeOverview {
  challengeEntry: {
    id: number;
    connectStatus: string;
    disqualified: boolean;
    enrollmentTime: string;
    initialBalance: number | null;
    balanceVerified: boolean;
  };
  challenge: any;
  connectedAccount: any;
  performance: {
    totalTrades: number;
    totalPnl: number;
    winningTrades: number;
    losingTrades: number;
    winRate: number;
    averagePnl: number;
    bestTrade: number;
    worstTrade: number;
  };
  leaderboardPosition: number | null;
  recentTrades: any[];
  connectionHealth: any[];
}

class DashboardService {
  /**
   * Get consolidated dashboard data
   * This replaces multiple API calls with a single optimized endpoint
   */
  async getDashboardData(): Promise<DashboardData> {
    try {
      const response = await apiService.get('/dashboard/data');
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch dashboard data');
      }
      
      return response.data;
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get challenge overview with performance metrics
   * Optimized endpoint for challenge-specific data
   */
  async getChallengeOverview(challengeId: number): Promise<ChallengeOverview> {
    try {
      const response = await apiService.get(`/dashboard/challenge/${challengeId}`);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch challenge overview');
      }
      
      return response.data;
    } catch (error) {
      console.error('Error fetching challenge overview:', error);
      throw error;
    }
  }

  /**
   * Get user summary statistics
   * Extracted from dashboard data for specific use cases
   */
  async getUserSummary() {
    try {
      const dashboardData = await this.getDashboardData();
      return {
        user: dashboardData.user,
        summary: dashboardData.summary
      };
    } catch (error) {
      console.error('Error fetching user summary:', error);
      throw error;
    }
  }

  /**
   * Get active challenge entries for the user
   * Filtered from dashboard data
   */
  async getActiveChallengeEntries() {
    try {
      const dashboardData = await this.getDashboardData();
      return dashboardData.challengeEntries.filter(entry => 
        !entry.disqualified && entry.connectStatus !== 'disconnected'
      );
    } catch (error) {
      console.error('Error fetching active challenge entries:', error);
      throw error;
    }
  }

  /**
   * Get recent trading activity
   * From dashboard data with additional filtering options
   */
  async getRecentActivity(limit?: number) {
    try {
      const dashboardData = await this.getDashboardData();
      const activity = dashboardData.recentActivity;
      
      return limit ? activity.slice(0, limit) : activity;
    } catch (error) {
      console.error('Error fetching recent activity:', error);
      throw error;
    }
  }

  /**
   * Get wallet information
   * Consolidated wallet data from dashboard
   */
  async getWalletInfo() {
    try {
      const dashboardData = await this.getDashboardData();
      return {
        credit: dashboardData.user.walletCredit,
        recentTransactions: dashboardData.walletTransactions
      };
    } catch (error) {
      console.error('Error fetching wallet info:', error);
      throw error;
    }
  }

  /**
   * Get notifications
   * From dashboard data with read/unread filtering
   */
  async getNotifications(unreadOnly: boolean = false) {
    try {
      const dashboardData = await this.getDashboardData();
      const notifications = dashboardData.notifications;
      
      return unreadOnly 
        ? notifications.filter(n => !n.seen)
        : notifications;
    } catch (error) {
      console.error('Error fetching notifications:', error);
      throw error;
    }
  }

  /**
   * Get performance metrics for a specific challenge
   * From challenge overview data
   */
  async getChallengePerformance(challengeId: number) {
    try {
      const overview = await this.getChallengeOverview(challengeId);
      return overview.performance;
    } catch (error) {
      console.error('Error fetching challenge performance:', error);
      throw error;
    }
  }

  /**
   * Check if user has any active challenges
   * Quick check from dashboard data
   */
  async hasActiveChallenges(): Promise<boolean> {
    try {
      const dashboardData = await this.getDashboardData();
      return dashboardData.summary.activeChallengeEntries > 0;
    } catch (error) {
      console.error('Error checking active challenges:', error);
      return false;
    }
  }

  /**
   * Get available challenges for enrollment
   * From dashboard data
   */
  async getAvailableChallenges() {
    try {
      const dashboardData = await this.getDashboardData();
      return dashboardData.activeChallenges.filter(challenge => {
        // Filter out challenges that are full or ended
        const now = new Date();
        const endDate = new Date(challenge.endDate);
        const isFull = challenge.maxParticipants && 
          challenge.currentParticipants >= challenge.maxParticipants;
        
        return endDate > now && !isFull;
      });
    } catch (error) {
      console.error('Error fetching available challenges:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const dashboardService = new DashboardService();
export default dashboardService;
