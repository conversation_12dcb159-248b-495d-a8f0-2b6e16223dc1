/**
 * Mock Data for Frontend Testing
 * @description Provides mock data to simulate backend responses for development
 */

import { ChallengeType, ChallengeStatus } from '@/features/challenges/types';

// Mock Users
export const mockUsers = [
  {
    id: '1',
    walletAddress: '******************************************',
    displayName: 'CryptoTrader_Pro',
    role: 'trader',
    cumulativeROI: 45.2,
    top3Finishes: 8,
    badges: ['🏆', '💎', '🔥']
  },
  {
    id: '2', 
    walletAddress: '******************************************',
    displayName: 'DiamondHands_Dave',
    role: 'trader',
    cumulativeROI: 32.1,
    top3Finishes: 5,
    badges: ['💎', '⚡']
  },
  {
    id: '3',
    walletAddress: '******************************************',
    displayName: 'TradingGuru_Alex',
    role: 'host',
    cumulativeROI: 78.9,
    top3Finishes: 15,
    badges: ['👑', '🏆', '💎', '🔥', '⚡']
  }
];

// Mock Challenges
export const mockChallenges = [
  {
    id: 1,
    name: "<PERSON>'s Crypto Showdown",
    description: "Join the ultimate crypto trading battle! Test your skills against the best traders in a 7-day intense competition.",
    hostWallet: '******************************************',
    hostName: 'TradingGuru_Alex',
    bannerUrl: 'https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=800&h=400&fit=crop',
    hostMessage: "Welcome to my arena! Only the strongest traders survive. Prove you have what it takes! 🔥",
    type: ChallengeType.WEEKLY,
    startDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
    endDate: new Date(Date.now() + 9 * 24 * 60 * 60 * 1000), // 9 days from now
    entryFee: 50,
    prizePool: 2500,
    participantCount: 47,
    maxParticipants: 100,
    status: ChallengeStatus.UPCOMING,
    ruleset: {
      initialBalance: 10000,
      maxDrawdownPercent: 20,
      maxRiskPerTradePercent: 5,
      noHedging: true,
      noMartingale: true,
      minTradeDurationMinutes: 5,
      minTrades: 10,
      maxDailyDrawdownPercent: 8,
      minTradingDays: 3
    }
  },
  {
    id: 2,
    name: "Daily Grind Challenge",
    description: "Fast-paced 24-hour trading sprint. Quick profits, quick glory!",
    hostWallet: '******************************************',
    hostName: 'SpeedTrader_Sam',
    bannerUrl: 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=800&h=400&fit=crop',
    hostMessage: "Speed is everything! Make every second count in this lightning-fast challenge! ⚡",
    type: ChallengeType.DAILY,
    startDate: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
    endDate: new Date(Date.now() + 18 * 60 * 60 * 1000), // 18 hours from now
    entryFee: 25,
    prizePool: 1250,
    participantCount: 89,
    maxParticipants: 150,
    status: ChallengeStatus.ACTIVE,
    ruleset: {
      initialBalance: 10000,
      maxDrawdownPercent: 15,
      maxRiskPerTradePercent: 8,
      noHedging: true,
      noMartingale: true,
      minTradeDurationMinutes: 2,
      minTrades: 5
    }
  },
  {
    id: 3,
    name: "Diamond Hands Marathon",
    description: "30-day endurance test for true diamond hands. Only the patient and skilled will prevail.",
    hostWallet: '******************************************',
    hostName: 'PatientProfits_Pete',
    bannerUrl: 'https://images.unsplash.com/photo-1640340434855-6084b1f4901c?w=800&h=400&fit=crop',
    hostMessage: "This is not for the weak. 30 days of pure skill and patience. Are you ready? 💎",
    type: ChallengeType.MONTHLY,
    startDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
    endDate: new Date(Date.now() + 35 * 24 * 60 * 60 * 1000), // 35 days from now
    entryFee: 100,
    prizePool: 8500,
    participantCount: 23,
    maxParticipants: 200,
    status: ChallengeStatus.UPCOMING,
    ruleset: {
      initialBalance: 10000,
      maxDrawdownPercent: 25,
      maxRiskPerTradePercent: 3,
      noHedging: true,
      noMartingale: true,
      minTradeDurationMinutes: 15,
      minTrades: 20,
      maxDailyDrawdownPercent: 5,
      minTradingDays: 15,
      minSwingTradeDays: 10
    }
  }
];

// Mock Leaderboard Data
export const mockLeaderboardEntries = [
  {
    userId: '1',
    username: 'CryptoTrader_Pro',
    rank: 1,
    score: 23.45,
    drawdown: 8.2,
    tradeCount: 47,
    winRate: 72.3,
    averagePnl: 0.89,
    change: 0,
    equity: 12345,
    badges: ['🏆', '💎', '🔥']
  },
  {
    userId: '2',
    username: 'DiamondHands_Dave',
    rank: 2,
    score: 18.92,
    drawdown: 12.1,
    tradeCount: 32,
    winRate: 65.6,
    averagePnl: 0.76,
    change: 1,
    equity: 11892,
    badges: ['💎', '⚡']
  },
  {
    userId: '4',
    username: 'QuickFlip_Quinn',
    rank: 3,
    score: 16.78,
    drawdown: 9.8,
    tradeCount: 89,
    winRate: 58.4,
    averagePnl: 0.34,
    change: -1,
    equity: 11678,
    badges: ['⚡']
  },
  {
    userId: '5',
    username: 'SteadyGains_Steve',
    rank: 4,
    score: 14.23,
    drawdown: 6.5,
    tradeCount: 28,
    winRate: 78.6,
    averagePnl: 1.12,
    change: 2,
    equity: 11423,
    badges: ['💎']
  },
  {
    userId: '6',
    username: 'RiskTaker_Rita',
    rank: 5,
    score: 12.67,
    drawdown: 18.9,
    tradeCount: 156,
    winRate: 51.3,
    averagePnl: 0.28,
    change: -2,
    equity: 11267,
    badges: ['🔥']
  }
];

// Mock API delay simulation
export const mockApiDelay = (ms: number = 500) => 
  new Promise(resolve => setTimeout(resolve, ms));

// Mock API responses
export const mockApi = {
  // Get all challenges
  getChallenges: async () => {
    await mockApiDelay();
    return {
      success: true,
      data: mockChallenges
    };
  },

  // Get challenge by ID
  getChallenge: async (id: number) => {
    await mockApiDelay();
    const challenge = mockChallenges.find(c => c.id === id);
    return {
      success: !!challenge,
      data: challenge,
      error: challenge ? null : 'Challenge not found'
    };
  },

  // Get leaderboard for challenge
  getLeaderboard: async (challengeId: number) => {
    await mockApiDelay();
    return {
      success: true,
      data: mockLeaderboardEntries
    };
  },

  // Get global leaderboard
  getGlobalLeaderboard: async () => {
    await mockApiDelay();
    return {
      success: true,
      data: mockLeaderboardEntries
    };
  },

  // Get user profile
  getUserProfile: async (userId: string) => {
    await mockApiDelay();
    const user = mockUsers.find(u => u.id === userId);
    return {
      success: !!user,
      data: user,
      error: user ? null : 'User not found'
    };
  }
};
