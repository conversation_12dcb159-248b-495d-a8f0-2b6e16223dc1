/**
 * Redux Store Configuration
 * @description Configures the Redux store with all slices and middleware
 * @version 1.0.0
 * @status stable
 */

import { configureStore } from '@reduxjs/toolkit';
import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';
import leaderboardReducer from '@/features/leaderboard/redux/leaderboardSlice';

/**
 * Configure the Redux store with all reducers and middleware
 */
export const store = configureStore({
  reducer: {
    leaderboard: leaderboardReducer,
    // Add more reducers here as needed
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore these action types
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Export a hook that can be reused to resolve types
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
