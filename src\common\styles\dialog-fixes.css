/* Dialog and Modal Fixes for UI Freezing Issues */

/* Ensure proper z-index stacking for dialogs */
[data-radix-dialog-overlay] {
  z-index: 50 !important;
}

[data-radix-dialog-content] {
  z-index: 51 !important;
}

/* Prevent pointer-events issues after dialog close */
[data-state="closed"][data-radix-dialog-overlay] {
  pointer-events: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

[data-state="closed"][data-radix-dialog-content] {
  pointer-events: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

/* Ensure proper cleanup of dialog overlays */
[data-radix-dialog-overlay][data-state="closed"] {
  animation-fill-mode: forwards !important;
}

/* Fix for potential scroll lock issues */
body[data-scroll-locked] {
  overflow: hidden !important;
}

/* Ensure dialogs don't interfere with page interactions when closed */
.dialog-overlay-cleanup {
  transition: opacity 0.2s ease-out, visibility 0.2s ease-out !important;
}

.dialog-overlay-cleanup[data-state="closed"] {
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none !important;
}

/* Fix for admin panel specific dialog issues */
.admin-panel [data-radix-dialog-overlay] {
  background-color: rgba(0, 0, 0, 0.8) !important;
  backdrop-filter: blur(4px) !important;
}

/* Ensure proper focus management */
[data-radix-dialog-content]:focus {
  outline: none !important;
}

/* Fix for potential animation conflicts */
[data-radix-dialog-overlay],
[data-radix-dialog-content] {
  animation-duration: 0.2s !important;
  animation-timing-function: ease-out !important;
}

/* Prevent scroll area conflicts in dialogs */
[data-radix-scroll-area-viewport] {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

[data-radix-scroll-area-viewport]::-webkit-scrollbar {
  width: 6px;
}

[data-radix-scroll-area-viewport]::-webkit-scrollbar-track {
  background: transparent;
}

[data-radix-scroll-area-viewport]::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

/* Fix for touch devices */
@media (hover: none) and (pointer: coarse) {
  [data-radix-dialog-overlay] {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }
}

/* Ensure proper cleanup on component unmount */
.dialog-cleanup-helper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
  opacity: 0;
}

/* Fix for potential memory leaks */
[data-radix-dialog-overlay][style*="pointer-events: none"] {
  display: none !important;
}

/* Ensure buttons remain clickable after dialog close */
button,
a,
[role="button"] {
  pointer-events: auto !important;
}

button:disabled,
a:disabled,
[role="button"]:disabled {
  pointer-events: none !important;
}

/* Fix for admin panel specific issues */
.admin-panel button:not(:disabled),
.admin-panel a:not(:disabled),
.admin-panel [role="button"]:not(:disabled) {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Ensure proper event propagation */
[data-radix-dialog-content] * {
  pointer-events: auto;
}

[data-radix-dialog-overlay][data-state="open"] {
  pointer-events: auto !important;
}

[data-radix-dialog-overlay][data-state="closed"] {
  pointer-events: none !important;
  display: none !important;
}

/* Fix for potential CSS conflicts */
.dialog-content-wrapper {
  position: relative;
  z-index: 52;
}

/* Ensure proper modal behavior */
[data-radix-dialog-portal] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 50;
}

[data-radix-dialog-portal][data-state="closed"] {
  display: none !important;
}

/* Fix for potential React state issues */
.react-dialog-cleanup {
  transition: all 0.2s ease-out;
}

.react-dialog-cleanup[data-state="closed"] {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  transform: scale(0.95);
}

/* Ensure proper cleanup timing */
[data-radix-dialog-overlay] {
  transition: opacity 0.2s ease-out, backdrop-filter 0.2s ease-out;
}

[data-radix-dialog-content] {
  transition: opacity 0.2s ease-out, transform 0.2s ease-out;
}
