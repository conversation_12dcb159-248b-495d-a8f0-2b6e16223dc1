import React from 'react';

const PrizePoolVisual = () => {
  return (
    <div className="mt-4 mb-6 bg-forex-dark/5 p-4 rounded-lg border border-forex-primary/20">
      <h4 className="text-sm font-semibold text-forex-dark mb-3">Prize Pool Distribution</h4>
      <div className="flex flex-col space-y-3">
        {/* Entry Fees */}
        <div className="flex items-center">
          <div className="w-full bg-gradient-to-r from-forex-primary/20 to-forex-accent/20 h-10 rounded-lg flex items-center justify-center text-xs font-medium text-forex-dark">
            100% Entry Fees
          </div>
        </div>
        
        {/* Split between community and platform */}
        <div className="flex items-center space-x-2">
          <div className="w-3/4 bg-gradient-to-r from-forex-primary to-forex-accent h-10 rounded-lg flex items-center justify-center text-xs font-medium text-white">
            75% Community Share
          </div>
          <div className="w-1/4 bg-forex-dark/30 h-10 rounded-lg flex items-center justify-center text-xs font-medium text-forex-dark">
            25% Platform
          </div>
        </div>
        
        {/* Community share breakdown */}
        <div className="flex items-center space-x-2">
          <div className="w-3/5 bg-forex-primary h-10 rounded-lg flex items-center justify-center text-xs font-medium text-white">
            60% Top Performers (1st, 2nd, 3rd)
          </div>
          <div className="w-2/5 bg-forex-accent h-10 rounded-lg flex items-center justify-center text-xs font-medium text-white">
            15% Wallet Credits (Top 30%)
          </div>
        </div>
        
        {/* Top performers breakdown */}
        <div className="flex items-center space-x-2">
          <div className="w-1/2 bg-forex-primary/80 h-8 rounded-lg flex items-center justify-center text-xs font-medium text-white">
            1st Place
          </div>
          <div className="w-3/10 bg-forex-primary/60 h-8 rounded-lg flex items-center justify-center text-xs font-medium text-white">
            2nd Place
          </div>
          <div className="w-1/5 bg-forex-primary/40 h-8 rounded-lg flex items-center justify-center text-xs font-medium text-white">
            3rd Place
          </div>
        </div>
      </div>
      
      <div className="mt-3 text-xs text-forex-neutral italic">
        *Exact distribution percentages vary by challenge type
      </div>
    </div>
  );
};

export default PrizePoolVisual;
