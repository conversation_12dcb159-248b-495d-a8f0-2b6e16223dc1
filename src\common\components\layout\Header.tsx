import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button, UserAvatar, ConnectionStatus } from '@/common/components/ui';
import { ConnectButton } from '@rainbow-me/rainbowkit';
import Logo from './Logo';
import { AuthenticationFlow } from '@/features/auth/components/AuthenticationFlow';
import { ChevronDown, User, LogOut, Wallet, Menu, X } from 'lucide-react';

const Header = () => {
  const location = useLocation();
  // Temporary simple auth state - replace with useAuth when working
  const isAuthenticated = false;
  const user: any = null;
  const getDisplayName = () => 'User';
  const logout = () => console.log('logout');

  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isWalletModalOpen, setIsWalletModalOpen] = useState(false);

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const handleLogout = () => {
    try {
      logout();
      setIsUserMenuOpen(false);
      console.log('Logout initiated from header');
    } catch (error) {
      console.error('Header logout error:', error);
    }
  };



  return (
    <>
      <header className="bg-forex-dark/95 backdrop-blur-md border-b border-forex-border/20 sticky top-0 z-40">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <Logo />

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              <Link
                to="/"
                className={`text-sm font-medium transition-all duration-200 hover:scale-105 ${
                  isActive('/')
                    ? 'text-forex-primary'
                    : 'text-white/80 hover:text-white'
                }`}
              >
                Home
              </Link>
              <Link
                to="/challenges"
                className={`text-sm font-medium transition-all duration-200 hover:scale-105 ${
                  isActive('/challenges')
                    ? 'text-forex-primary'
                    : 'text-white/80 hover:text-white'
                }`}
              >
                Challenges
              </Link>
              <a
                href="/#pricing"
                className="text-sm font-medium text-white/80 hover:text-white transition-all duration-200 hover:scale-105"
              >
                Pricing
              </a>
              <a
                href="/#faq"
                className="text-sm font-medium text-white/80 hover:text-white transition-all duration-200 hover:scale-105"
              >
                FAQ
              </a>
            </nav>

            {/* Desktop Auth Section */}
            <div className="hidden md:flex items-center space-x-4">
              {isAuthenticated ? (
                <div className="relative">
                  <button
                    onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                    className="flex items-center space-x-3 bg-forex-card/50 hover:bg-forex-card/80 border border-forex-border/30 rounded-xl px-4 py-2 transition-all duration-200 hover:scale-105 hover-lift"
                  >
                    <UserAvatar
                      address={user?.walletAddress}
                      displayName={user?.displayName}
                      role={user?.role}
                      size="sm"
                      showStatus={true}
                      isOnline={true}
                    />
                    <div className="flex flex-col items-start">
                      <span className="text-sm text-white font-medium">{getDisplayName()}</span>
                      <ConnectionStatus status="connected" size="sm" showIcon={false} message="Connected" />
                    </div>
                    <ChevronDown className={`w-4 h-4 text-white/60 transition-transform duration-200 ${isUserMenuOpen ? 'rotate-180' : ''}`} />
                  </button>

                  {/* User Dropdown */}
                  {isUserMenuOpen && (
                    <div className="absolute right-0 top-full mt-2 w-48 bg-forex-dark border border-forex-border/30 rounded-xl shadow-xl py-2 z-50">
                      <Link
                        to="/profile"
                        className="flex items-center space-x-2 px-4 py-2 text-sm text-white/80 hover:text-white hover:bg-forex-card/30 transition-colors"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <User className="w-4 h-4" />
                        <span>Profile</span>
                      </Link>
                      <button
                        onClick={handleLogout}
                        className="flex items-center space-x-2 px-4 py-2 text-sm text-white/80 hover:text-white hover:bg-forex-card/30 transition-colors w-full text-left"
                      >
                        <LogOut className="w-4 h-4" />
                        <span>Logout</span>
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex items-center space-x-3">
                  {/* Wallet Connection and Authentication Flow */}
                  {!isAuthenticated ? (
                    // Not authenticated - show wallet modal button
                    <Button
                      onClick={() => setIsWalletModalOpen(true)}
                      variant="outline"
                      size="sm"
                      className="border-forex-border/30 text-white hover:bg-forex-primary/10 hover:border-forex-primary/50 transition-all duration-200 hover:scale-105"
                    >
                      <Wallet className="w-4 h-4 mr-2" />
                      Connect Wallet
                    </Button>
                  ) : (
                    // Connected and authenticated - show wallet info
                    <ConnectButton.Custom>
                      {({ account, chain, openAccountModal, openChainModal }) => (
                        <div className="flex items-center space-x-2">
                          {chain?.unsupported ? (
                            <Button
                              onClick={openChainModal}
                              variant="destructive"
                              size="sm"
                            >
                              Wrong Network
                            </Button>
                          ) : (
                            <Button
                              onClick={openAccountModal}
                              variant="outline"
                              size="sm"
                              className="border-forex-border/30 text-white hover:bg-forex-primary/10 hover:border-forex-primary/50"
                            >
                              {account?.displayName || `${account?.address?.slice(0, 6)}...${account?.address?.slice(-4)}`}
                            </Button>
                          )}
                        </div>
                      )}
                    </ConnectButton.Custom>
                  )}
                  <a href="/#pricing">
                    <Button
                      size="sm"
                      className="bg-gradient-to-r from-forex-primary to-forex-hover hover:from-forex-hover hover:to-forex-primary text-white transition-all duration-200 hover:scale-105 shadow-lg"
                    >
                      Get Started
                    </Button>
                  </a>
                </div>
              )}
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden text-white/80 hover:text-white transition-colors p-2"
            >
              {isMobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
            </button>
          </div>

          {/* Mobile Menu */}
          {isMobileMenuOpen && (
            <div className="md:hidden border-t border-forex-border/20 py-4">
              <nav className="space-y-4">
                <Link
                  to="/"
                  className={`block text-sm font-medium transition-colors ${
                    isActive('/')
                      ? 'text-forex-primary'
                      : 'text-white/80 hover:text-white'
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Home
                </Link>
                <Link
                  to="/challenges"
                  className={`block text-sm font-medium transition-colors ${
                    isActive('/challenges')
                      ? 'text-forex-primary'
                      : 'text-white/80 hover:text-white'
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Challenges
                </Link>
                <a
                  href="/#pricing"
                  className="block text-sm font-medium text-white/80 hover:text-white transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Pricing
                </a>
                <a
                  href="/#faq"
                  className="block text-sm font-medium text-white/80 hover:text-white transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  FAQ
                </a>

                {/* Mobile Auth */}
                <div className="pt-4 border-t border-forex-border/20">
                  {isAuthenticated ? (
                    <div className="space-y-3">
                      <Link
                        to="/profile"
                        className="block text-sm text-white/80 hover:text-white transition-colors"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        {getDisplayName()}
                      </Link>
                      <button
                        onClick={() => {
                          handleLogout();
                          setIsMobileMenuOpen(false);
                        }}
                        className="block text-sm text-white/80 hover:text-white transition-colors"
                      >
                        Logout
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {/* Mobile Wallet Connection and Authentication Flow */}
                      {!isAuthenticated ? (
                        // Not authenticated - show wallet modal button
                        <Button
                          onClick={() => {
                            setIsWalletModalOpen(true);
                            setIsMobileMenuOpen(false);
                          }}
                          variant="outline"
                          size="sm"
                          className="w-full border-forex-border text-white hover:bg-forex-primary/10"
                        >
                          <Wallet className="w-4 h-4 mr-2" />
                          Connect Wallet
                        </Button>
                      ) : (
                        // Connected and authenticated - show wallet info
                        <ConnectButton.Custom>
                          {({ account, chain, openAccountModal, openChainModal }) => (
                            <div className="space-y-2">
                              {chain?.unsupported ? (
                                <Button
                                  onClick={() => {
                                    openChainModal();
                                    setIsMobileMenuOpen(false);
                                  }}
                                  variant="destructive"
                                  size="sm"
                                  className="w-full"
                                >
                                  Wrong Network
                                </Button>
                              ) : (
                                <Button
                                  onClick={() => {
                                    openAccountModal();
                                    setIsMobileMenuOpen(false);
                                  }}
                                  variant="outline"
                                  size="sm"
                                  className="w-full border-forex-border text-white hover:bg-forex-primary/10"
                                >
                                  {account?.displayName || `${account?.address?.slice(0, 6)}...${account?.address?.slice(-4)}`}
                                </Button>
                              )}
                            </div>
                          )}
                        </ConnectButton.Custom>
                      )}
                      <a href="/#pricing" className="block">
                        <Button
                          size="sm"
                          className="w-full bg-forex-primary hover:bg-forex-hover text-white"
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          Get Started
                        </Button>
                      </a>
                    </div>
                  )}
                </div>
              </nav>
            </div>
          )}
        </div>
      </header>

      {/* Wallet Modal */}
      {isWalletModalOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="relative">
            <button
              onClick={() => setIsWalletModalOpen(false)}
              className="absolute -top-2 -right-2 bg-forex-dark border border-forex-border/30 rounded-full p-2 text-white/60 hover:text-white hover:bg-forex-card/50 transition-colors z-10"
            >
              <X className="w-4 h-4" />
            </button>
            <AuthenticationFlow
              onAuthenticated={() => {
                setIsWalletModalOpen(false);
                // Optionally navigate somewhere after authentication
              }}
              className="max-w-md"
            />
          </div>
        </div>
      )}
    </>
  );
};

export default Header;
