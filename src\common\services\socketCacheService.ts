import { socketService } from './socketService';
import { SocketEvent } from '@/types/socketEvents';

/**
 * Cache entry interface
 */
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

/**
 * Socket cache service
 * 
 * Provides caching for WebSocket data to improve performance and reduce redundant updates
 */
class SocketCacheService {
  private cache: Map<string, CacheEntry<any>> = new Map();
  private listeners: Map<string, Set<(data: any) => void>> = new Map();
  private defaultTTL: number = 60 * 1000; // 1 minute default TTL
  private isInitialized: boolean = false;
  
  /**
   * Initialize the cache service
   */
  public init(): void {
    if (this.isInitialized) return;
    
    // Set up event listeners for different socket events
    this.setupEventListeners();
    
    this.isInitialized = true;
    console.log('Socket cache service initialized');
  }
  
  /**
   * Set up event listeners for different socket events
   */
  private setupEventListeners(): void {
    // Trade events
    socketService.on(SocketEvent.TRADE_CREATED, (data) => {
      this.updateCache('trades', (trades) => [data, ...(trades || [])]);
      this.updateCache(`trade:${data.id}`, data);
    });
    
    socketService.on(SocketEvent.TRADE_UPDATED, (data) => {
      this.updateCache('trades', (trades) => {
        if (!trades) return [data];
        return trades.map(trade => trade.id === data.id ? data : trade);
      });
      this.updateCache(`trade:${data.id}`, data);
    });
    
    // Metrics events
    socketService.on(SocketEvent.USER_METRICS_UPDATED, (data) => {
      this.updateCache('metrics', data);
    });
    
    // Drawdown events
    socketService.on(SocketEvent.REAL_TIME_DRAWDOWN_UPDATE, (data) => {
      this.updateCache('drawdown', data);
    });
  }
  
  /**
   * Get data from cache
   * @param key - Cache key
   * @returns Cached data or null if not found or expired
   */
  public get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    // Return null if entry doesn't exist
    if (!entry) return null;
    
    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }
  
  /**
   * Set data in cache
   * @param key - Cache key
   * @param data - Data to cache
   * @param ttl - Time to live in milliseconds (optional)
   */
  public set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
    
    // Notify listeners
    this.notifyListeners(key, data);
  }
  
  /**
   * Update cache with a transform function
   * @param key - Cache key
   * @param transform - Transform function to update data
   * @param ttl - Time to live in milliseconds (optional)
   */
  public updateCache<T>(key: string, transformOrData: ((data: T | null) => T) | T, ttl: number = this.defaultTTL): void {
    const currentData = this.get<T>(key);
    
    let newData: T;
    if (typeof transformOrData === 'function') {
      newData = (transformOrData as (data: T | null) => T)(currentData);
    } else {
      newData = transformOrData;
    }
    
    this.set(key, newData, ttl);
  }
  
  /**
   * Subscribe to cache updates
   * @param key - Cache key
   * @param listener - Listener function
   * @returns Unsubscribe function
   */
  public subscribe<T>(key: string, listener: (data: T) => void): () => void {
    // Create listener set if it doesn't exist
    if (!this.listeners.has(key)) {
      this.listeners.set(key, new Set());
    }
    
    // Add listener
    this.listeners.get(key)!.add(listener);
    
    // Return unsubscribe function
    return () => {
      const listeners = this.listeners.get(key);
      if (listeners) {
        listeners.delete(listener);
        if (listeners.size === 0) {
          this.listeners.delete(key);
        }
      }
    };
  }
  
  /**
   * Notify listeners of cache updates
   * @param key - Cache key
   * @param data - Updated data
   */
  private notifyListeners<T>(key: string, data: T): void {
    const listeners = this.listeners.get(key);
    if (listeners) {
      listeners.forEach(listener => listener(data));
    }
  }
  
  /**
   * Clear cache
   * @param key - Optional key to clear specific cache entry
   */
  public clear(key?: string): void {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }
  
  /**
   * Get cache size
   * @returns Number of entries in cache
   */
  public size(): number {
    return this.cache.size;
  }
  
  /**
   * Get cache keys
   * @returns Array of cache keys
   */
  public keys(): string[] {
    return Array.from(this.cache.keys());
  }
  
  /**
   * Check if cache has key
   * @param key - Cache key
   * @returns True if cache has key and it's not expired
   */
  public has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }
}

// Export singleton instance
export const socketCacheService = new SocketCacheService();
