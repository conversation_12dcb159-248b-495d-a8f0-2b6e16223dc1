/**
 * Error Handling Utilities
 * @description Utility functions for error handling
 * @version 1.0.0
 * @status stable
 */

import React, { ReactNode } from 'react';
import ErrorBoundary from '@/common/components/ErrorBoundary';
import { ApiError } from '@/common/services/api';

/**
 * Wrap a component with an error boundary
 * @param children - The component to wrap
 * @param componentName - The name of the component (for error reporting)
 * @param containError - Whether to contain the error in a smaller UI
 * @returns The wrapped component
 */
export const withErrorBoundary = (
  children: ReactNode,
  componentName?: string,
  containError: boolean = true
): ReactNode => {
  return (
    <ErrorBoundary
      componentName={componentName}
      containError={containError}
    >
      {children}
    </ErrorBoundary>
  );
};

/**
 * Format an error message for display
 * @param error - The error object
 * @returns A formatted error message
 */
export const formatErrorMessage = (error: unknown): string => {
  if (typeof error === 'string') {
    return error;
  }
  
  if (error instanceof ApiError) {
    return `${error.message}${error.code ? ` (${error.code})` : ''}`;
  }
  
  if (error instanceof Error) {
    return error.message;
  }
  
  return 'An unexpected error occurred';
};

/**
 * Check if an error is a network error
 * @param error - The error to check
 * @returns Whether the error is a network error
 */
export const isNetworkError = (error: unknown): boolean => {
  if (error instanceof ApiError && error.code === 'NETWORK_ERROR') {
    return true;
  }
  
  if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
    return true;
  }
  
  if (error instanceof Error && 
      (error.message.includes('network') || 
       error.message.includes('connection') || 
       error.message.includes('offline'))) {
    return true;
  }
  
  return false;
};

/**
 * Get a user-friendly error message based on error type
 * @param error - The error object
 * @returns A user-friendly error message
 */
export const getUserFriendlyErrorMessage = (error: unknown): string => {
  if (isNetworkError(error)) {
    return 'Network error: Please check your internet connection and try again.';
  }
  
  if (error instanceof ApiError) {
    switch (error.status) {
      case 401:
        return 'Authentication error: Please log in again.';
      case 403:
        return 'Access denied: You do not have permission to perform this action.';
      case 404:
        return 'Resource not found: The requested item could not be found.';
      case 429:
        return 'Too many requests: Please try again later.';
      case 500:
      case 502:
      case 503:
      case 504:
        return 'Server error: Our system is experiencing issues. Please try again later.';
      default:
        return error.message || 'An unexpected error occurred.';
    }
  }
  
  return formatErrorMessage(error);
};
