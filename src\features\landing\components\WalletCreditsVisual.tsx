import React from 'react';
import { <PERSON><PERSON>, Clock, CheckCircle2, XCir<PERSON>, Trophy } from 'lucide-react';

const WalletCreditsVisual = () => {
  return (
    <div className="mt-4 mb-6 bg-gradient-to-b from-forex-dark to-[#0a1a2f] p-4 rounded-lg border border-forex-primary/20">
      <h4 className="text-sm font-semibold text-white mb-3">How Wallet Credits Work</h4>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Earning Credits */}
        <div className="bg-forex-card/80 backdrop-blur-md p-3 rounded-lg border border-forex-border/30 shadow-sm">
          <div className="flex items-center mb-2">
            <div className="w-8 h-8 rounded-full bg-forex-primary/10 flex items-center justify-center mr-2">
              <Trophy className="w-4 h-4 text-forex-primary" />
            </div>
            <h5 className="text-sm font-medium text-white">Earning Credits</h5>
          </div>
          <ul className="text-xs space-y-2 text-forex-light/80">
            <li className="flex items-start">
              <CheckCircle2 className="w-3.5 h-3.5 text-green-500 mt-0.5 mr-1.5 flex-shrink-0" />
              <span>Finish in top 30% of any challenge</span>
            </li>
            <li className="flex items-start">
              <CheckCircle2 className="w-3.5 h-3.5 text-green-500 mt-0.5 mr-1.5 flex-shrink-0" />
              <span>Credits automatically added to wallet</span>
            </li>
            <li className="flex items-start">
              <CheckCircle2 className="w-3.5 h-3.5 text-green-500 mt-0.5 mr-1.5 flex-shrink-0" />
              <span>Higher placement = more credits</span>
            </li>
          </ul>
        </div>

        {/* Using Credits */}
        <div className="bg-forex-card/80 backdrop-blur-md p-3 rounded-lg border border-forex-border/30 shadow-sm">
          <div className="flex items-center mb-2">
            <div className="w-8 h-8 rounded-full bg-forex-accent/10 flex items-center justify-center mr-2">
              <Wallet className="w-4 h-4 text-forex-accent" />
            </div>
            <h5 className="text-sm font-medium text-white">Using Credits</h5>
          </div>
          <ul className="text-xs space-y-2 text-forex-light/80">
            <li className="flex items-start">
              <CheckCircle2 className="w-3.5 h-3.5 text-green-500 mt-0.5 mr-1.5 flex-shrink-0" />
              <span>Pay for future challenge entries</span>
            </li>
            <li className="flex items-start">
              <CheckCircle2 className="w-3.5 h-3.5 text-green-500 mt-0.5 mr-1.5 flex-shrink-0" />
              <span>Combine with crypto for partial payments</span>
            </li>
            <li className="flex items-start">
              <XCircle className="w-3.5 h-3.5 text-red-500 mt-0.5 mr-1.5 flex-shrink-0" />
              <span>Cannot be withdrawn as cryptocurrency</span>
            </li>
          </ul>
        </div>
      </div>

      {/* Expiration Timeline */}
      <div className="mt-4 bg-forex-card/80 backdrop-blur-md p-3 rounded-lg border border-forex-border/30 shadow-sm">
        <div className="flex items-center mb-2">
          <div className="w-8 h-8 rounded-full bg-forex-dark/10 flex items-center justify-center mr-2">
            <Clock className="w-4 h-4 text-forex-light" />
          </div>
          <h5 className="text-sm font-medium text-white">Credit Expiration</h5>
        </div>

        <div className="relative pt-1">
          <div className="flex mb-2 items-center justify-between">
            <div>
              <span className="text-xs font-semibold inline-block text-forex-primary">
                Active
              </span>
            </div>
            <div>
              <span className="text-xs font-semibold inline-block text-red-500">
                Expired
              </span>
            </div>
          </div>
          <div className="overflow-hidden h-2 mb-1 text-xs flex rounded bg-forex-dark">
            <div style={{ width: "100%" }} className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-forex-primary to-red-500"></div>
          </div>
          <div className="flex justify-between text-xs text-forex-light/80">
            <span>Day 1</span>
            <span>Day 15</span>
            <span>Day 30</span>
          </div>
          <p className="text-xs text-forex-light/80 mt-2 italic">
            Credits expire 30 days after inactivity (no challenge participation)
          </p>
        </div>
      </div>
    </div>
  );
};

export default WalletCreditsVisual;
