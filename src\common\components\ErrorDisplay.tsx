/**
 * Error Display Component
 * @description A reusable component for displaying errors with user-friendly messages and actions
 * @version 1.0.0
 * @status stable
 */

import React from 'react';
import { useNavigate } from 'react-router-dom';
import { AlertCircle, RefreshCw, Home, HelpCircle } from 'lucide-react';
import { Alert, AlertTitle, AlertDescription } from '@/common/components/ui/alert';
import { Button } from '@/common/components/ui/button';

export interface ErrorDisplayProps {
  /** The error message to display */
  message: string;
  /** Optional title for the error alert */
  title?: string;
  /** Optional CSS class name */
  className?: string;
  /** Optional callback to retry the operation */
  onRetry?: () => void;
  /** Optional flag to show a home button */
  showHomeButton?: boolean;
  /** Optional flag to show a support button */
  showSupportButton?: boolean;
  /** Optional flag to show a dashboard button */
  showDashboardButton?: boolean;
  /** Optional flag to show a wallet button */
  showWalletButton?: boolean;
  /** Optional flag to show a challenges button */
  showChallengesButton?: boolean;
  /** Optional variant for the alert */
  variant?: 'default' | 'destructive' | 'warning';
}

/**
 * A reusable component for displaying errors with user-friendly messages and actions
 */
const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  message,
  title = 'We encountered an issue',
  className = '',
  onRetry,
  showHomeButton = false,
  showSupportButton = false,
  showDashboardButton = false,
  showWalletButton = false,
  showChallengesButton = false,
  variant = 'destructive',
}) => {
  const navigate = useNavigate();

  // Determine which action buttons to show based on the error message
  const shouldShowDashboardButton = showDashboardButton || message.includes('already entered');
  const shouldShowWalletButton = showWalletButton || message.includes('insufficient credits');
  const shouldShowChallengesButton = showChallengesButton || message.includes('challenge not found');
  const shouldShowSupportButton = showSupportButton || message.includes('contact support');

  return (
    <Alert variant={variant} className={`mb-4 ${className}`}>
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>{title}</AlertTitle>
      <AlertDescription className="mt-2">
        <div className="text-sm font-medium">{message}</div>
        
        {/* Action buttons */}
        <div className="mt-4 flex flex-wrap gap-2">
          {onRetry && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onRetry}
              className="flex items-center"
            >
              <RefreshCw className="mr-1 h-3 w-3" />
              Try Again
            </Button>
          )}
          
          {showHomeButton && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => navigate('/')}
              className="flex items-center"
            >
              <Home className="mr-1 h-3 w-3" />
              Home
            </Button>
          )}
          
          {shouldShowDashboardButton && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => navigate('/dashboard')}
              className="flex items-center"
            >
              Dashboard
            </Button>
          )}
          
          {shouldShowWalletButton && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => navigate('/wallet')}
              className="flex items-center"
            >
              Wallet
            </Button>
          )}
          
          {shouldShowChallengesButton && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => navigate('/dashboard/challenges')}
              className="flex items-center"
            >
              Browse Challenges
            </Button>
          )}
          
          {shouldShowSupportButton && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => window.open('mailto:<EMAIL>', '_blank')}
              className="flex items-center"
            >
              <HelpCircle className="mr-1 h-3 w-3" />
              Contact Support
            </Button>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
};

export default ErrorDisplay;
