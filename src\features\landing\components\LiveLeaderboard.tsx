import React from 'react';
import { Trophy, TrendingUp, TrendingDown, Crown, Medal, Award } from 'lucide-react';

interface LeaderboardEntry {
  rank: number;
  walletAddress: string;
  displayName?: string;
  roi: number;
  equity: number;
  badges?: string[];
  isCurrentUser?: boolean;
}

interface LiveLeaderboardProps {
  entries: LeaderboardEntry[];
  challengeName?: string;
  prizePool?: number;
  className?: string;
}

export const LiveLeaderboard: React.FC<LiveLeaderboardProps> = ({
  entries,
  challengeName,
  prizePool,
  className = ''
}) => {
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-5 h-5 text-accent-orange" />;
      case 2:
        return <Medal className="w-5 h-5 text-neutral-400" />;
      case 3:
        return <Award className="w-5 h-5 text-accent-orange" />;
      default:
        return <span className="text-text-tertiary font-bold">#{rank}</span>;
    }
  };

  const getRankBg = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-accent-orange/20 to-accent-pink/20 border-accent-orange/30';
      case 2:
        return 'bg-gradient-to-r from-neutral-400/20 to-neutral-300/20 border-neutral-400/30';
      case 3:
        return 'bg-gradient-to-r from-accent-orange/15 to-accent-orange/10 border-accent-orange/20';
      default:
        return 'bg-bg-tertiary border-neutral-800';
    }
  };

  const formatROI = (roi: number) => {
    const sign = roi >= 0 ? '+' : '';
    const color = roi >= 0 ? 'text-accent-emerald' : 'text-error';
    const icon = roi >= 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />;
    
    return (
      <div className={`flex items-center gap-1 ${color}`}>
        {icon}
        <span className="font-bold">{sign}{roi.toFixed(2)}%</span>
      </div>
    );
  };

  const formatWalletAddress = (address: string, displayName?: string) => {
    if (displayName) return displayName;
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  return (
    <div className={`card-premium ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-bold text-text-primary flex items-center gap-2">
            <Trophy className="w-5 h-5 text-accent-orange" />
            Live Leaderboard
          </h3>
          {challengeName && (
            <p className="text-sm text-text-tertiary mt-1">{challengeName}</p>
          )}
        </div>
        {prizePool && (
          <div className="text-right">
            <div className="text-sm text-text-tertiary">Prize Pool</div>
            <div className="text-lg font-bold text-accent-emerald">
              ${prizePool.toLocaleString()}
            </div>
          </div>
        )}
      </div>

      {/* Leaderboard Entries */}
      <div className="space-y-3">
        {entries.map((entry, index) => (
          <div
            key={`${entry.walletAddress}-${entry.rank}`}
            className={`
              p-4 rounded-lg border transition-all duration-300 hover:scale-[1.02]
              ${getRankBg(entry.rank)}
              ${entry.isCurrentUser ? 'ring-2 ring-primary-500/50' : ''}
            `}
          >
            <div className="flex items-center justify-between">
              {/* Left: Rank & User Info */}
              <div className="flex items-center gap-4">
                <div className="flex items-center justify-center w-8 h-8">
                  {getRankIcon(entry.rank)}
                </div>
                
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-text-primary">
                      {formatWalletAddress(entry.walletAddress, entry.displayName)}
                    </span>
                    {entry.isCurrentUser && (
                      <span className="px-2 py-0.5 bg-primary-500/20 text-primary-400 text-xs rounded-full">
                        You
                      </span>
                    )}
                  </div>
                  
                  {/* Badges */}
                  {entry.badges && entry.badges.length > 0 && (
                    <div className="flex gap-1 mt-1">
                      {entry.badges.map((badge, badgeIndex) => (
                        <span
                          key={badgeIndex}
                          className="px-2 py-0.5 bg-accent-purple/20 text-accent-purple text-xs rounded-full"
                        >
                          {badge}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Right: Performance Stats */}
              <div className="text-right">
                <div className="flex items-center gap-4">
                  <div>
                    <div className="text-sm text-text-tertiary">Equity</div>
                    <div className="font-bold text-text-primary">
                      ${entry.equity.toLocaleString()}
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-sm text-text-tertiary">ROI</div>
                    {formatROI(entry.roi)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Footer */}
      <div className="mt-6 pt-4 border-t border-neutral-800">
        <div className="flex items-center justify-between text-sm text-text-tertiary">
          <span>Updates every 5 seconds</span>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-accent-emerald rounded-full animate-pulse" />
            <span>Live</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LiveLeaderboard;
