import * as React from "react"
import * as CheckboxPrimitive from "@radix-ui/react-checkbox"
import { Check } from "lucide-react"

import { cn } from "@/lib/utils"

/**
 * Checkbox component for form inputs
 * 
 * A styled checkbox component that supports all standard checkbox functionality.
 * Built on top of Radix UI's Checkbox primitive for accessibility.
 * 
 * @component
 * @status stable
 * @version 1.0.0
 * 
 * @example
 * // Basic usage
 * <Checkbox id="terms" />
 * 
 * @example
 * // With label and onChange handler
 * <div className="flex items-center space-x-2">
 *   <Checkbox id="terms" checked={checked} onCheckedChange={setChecked} />
 *   <label htmlFor="terms">Accept terms and conditions</label>
 * </div>
 */
const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>
>(({ className, ...props }, ref) => (
  <CheckboxPrimitive.Root
    ref={ref}
    className={cn(
      "peer h-4 w-4 shrink-0 rounded-sm border border-forex-neutral/50 focus:outline-none focus:ring-2 focus:ring-forex-primary focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-forex-primary data-[state=checked]:text-white",
      className
    )}
    {...props}
  >
    <CheckboxPrimitive.Indicator
      className={cn("flex items-center justify-center")}
    >
      <Check className="h-3 w-3" />
    </CheckboxPrimitive.Indicator>
  </CheckboxPrimitive.Root>
))
Checkbox.displayName = CheckboxPrimitive.Root.displayName

export { Checkbox }
