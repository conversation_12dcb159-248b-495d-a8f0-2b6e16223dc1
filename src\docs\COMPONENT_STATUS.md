# Component Status System

To help track the development status of components in the TradeChampionX codebase, we use a status indicator system in the JSDoc comments of each component.

## Status Levels

### 1. Experimental

Components marked as `@status experimental` are:
- In early development
- Subject to significant changes
- Not recommended for production use
- May have incomplete features or documentation

```tsx
/**
 * NewFeature component - Still in development
 * 
 * @component
 * @status experimental
 */
```

### 2. Beta

Components marked as `@status beta` are:
- Feature complete but not fully tested
- May undergo minor API changes
- Usable in production with caution
- Have complete documentation

```tsx
/**
 * TradeChart component - Ready for testing
 * 
 * @component
 * @status beta
 */
```

### 3. Stable

Components marked as `@status stable` are:
- Fully tested and reliable
- Have a stable API that won't change without deprecation notices
- Safe for production use
- Have comprehensive documentation and examples

```tsx
/**
 * Button component - Production ready
 * 
 * @component
 * @status stable
 */
```

### 4. Deprecated

Components marked as `@status deprecated` are:
- Scheduled for removal in a future version
- Should not be used in new code
- Have replacement components that should be used instead
- Will be maintained only for backward compatibility

```tsx
/**
 * OldComponent - Use NewComponent instead
 * 
 * @component
 * @status deprecated
 * @see NewComponent
 */
```

## Version Information

In addition to status, components should include version information:

```tsx
/**
 * Component description
 * 
 * @component
 * @version 1.2.0
 * @status stable
 */
```

## Implementation

To implement this system:

1. Add `@status` and `@version` tags to all component JSDoc comments
2. Update status as components mature
3. Include migration instructions for deprecated components
4. Review component statuses regularly during development cycles

## Visual Indicators

When using Storybook or other component documentation tools, consider adding visual indicators based on status:
- Experimental: Yellow warning badge
- Beta: Blue info badge
- Stable: Green success badge
- Deprecated: Red danger badge
