import * as React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { DayPicker } from "react-day-picker";
import { cn } from "@/common/utils";

export type CalendarProps = React.ComponentProps<typeof DayPicker>;

/**
 * Calendar component
 *
 * A date picker component that allows users to select dates.
 *
 * @component
 * @status stable
 * @version 1.0.0
 *
 * @example
 * // Basic usage
 * <Calendar
 *   mode="single"
 *   selected={date}
 *   onSelect={setDate}
 *   className="rounded-md border"
 * />
 *
 * @example
 * // Range selection
 * <Calendar
 *   mode="range"
 *   selected={dateRange}
 *   onSelect={setDateRange}
 *   numberOfMonths={2}
 * />
 */
function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn("p-3", className)}
      classNames={{
        months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
        month: "space-y-4",
        caption: "flex justify-center pt-1 relative items-center",
        caption_label: "text-sm font-medium text-white",
        nav: "space-x-1 flex items-center",
        nav_button: cn(
          "h-7 w-7 bg-transparent p-0 text-white hover:bg-forex-hover/20 hover:rounded"
        ),
        nav_button_previous: "absolute left-1",
        nav_button_next: "absolute right-1",
        table: "w-full border-collapse space-y-1",
        head_row: "flex",
        head_cell:
          "text-forex-neutral rounded-md w-9 font-normal text-[0.8rem]",
        row: "flex w-full mt-2",
        cell: "h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-forex-card/50 [&:has([aria-selected])]:bg-forex-card first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
        day: cn(
          "h-9 w-9 p-0 font-normal text-white aria-selected:opacity-100 hover:bg-forex-hover/50 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-forex-primary focus:ring-offset-2 focus:ring-offset-forex-dark"
        ),
        day_range_end: "day-range-end",
        day_selected:
          "bg-forex-primary text-white hover:bg-forex-hover hover:text-white focus:bg-forex-primary focus:text-white",
        day_today: "bg-forex-dark text-white font-bold",
        day_outside:
          "day-outside text-forex-neutral opacity-50 aria-selected:bg-forex-card/50 aria-selected:text-forex-neutral aria-selected:opacity-30",
        day_disabled: "text-forex-neutral opacity-50",
        day_range_middle:
          "aria-selected:bg-forex-card aria-selected:text-forex-neutral",
        day_hidden: "invisible",
        ...classNames,
      }}
      components={{
        IconLeft: () => <ChevronLeft className="h-4 w-4 text-white" />,
        IconRight: () => <ChevronRight className="h-4 w-4 text-white" />,
      }}
      {...props}
    />
  );
}
Calendar.displayName = "Calendar";

export { Calendar };
