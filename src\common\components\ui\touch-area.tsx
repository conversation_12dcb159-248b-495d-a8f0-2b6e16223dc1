import React, { forwardRef } from 'react';
import { motion, MotionProps } from 'framer-motion';
import { cn } from '@/common/utils';
import { useResponsive } from '@/common/context';

/**
 * Props for the TouchArea component
 */
export interface TouchAreaProps extends React.HTMLAttributes<HTMLDivElement>, MotionProps {
  /** Whether to disable touch effects */
  disableEffects?: boolean;
  /** Whether to use a larger touch area */
  largeArea?: boolean;
  /** Whether to use a subtle effect */
  subtle?: boolean;
  /** Whether to use a glow effect */
  glow?: boolean;
  /** Whether to use a ripple effect */
  ripple?: boolean;
  /** Whether to use a scale effect */
  scale?: boolean;
  /** Whether to use a rotate effect */
  rotate?: boolean;
  /** Whether to use a bounce effect */
  bounce?: boolean;
  /** Whether to use a press effect */
  press?: boolean;
  /** Whether to use a hover effect */
  hover?: boolean;
  /** Whether to use a tap effect */
  tap?: boolean;
  /** Whether to use a focus effect */
  focus?: boolean;
  /** Whether to use a active effect */
  active?: boolean;
  /** Whether to use a disabled effect */
  disabled?: boolean;
  /** Whether to use a loading effect */
  loading?: boolean;
  /** Whether to use a success effect */
  success?: boolean;
  /** Whether to use a error effect */
  error?: boolean;
  /** Whether to use a warning effect */
  warning?: boolean;
  /** Whether to use a info effect */
  info?: boolean;
  /** Whether to use a primary effect */
  primary?: boolean;
  /** Whether to use a secondary effect */
  secondary?: boolean;
  /** Whether to use a tertiary effect */
  tertiary?: boolean;
  /** Whether to use a quaternary effect */
  quaternary?: boolean;
  /** Whether to use a quinary effect */
  quinary?: boolean;
  /** Whether to use a senary effect */
  senary?: boolean;
  /** Whether to use a septenary effect */
  septenary?: boolean;
  /** Whether to use a octonary effect */
  octonary?: boolean;
  /** Whether to use a nonary effect */
  nonary?: boolean;
  /** Whether to use a denary effect */
  denary?: boolean;
}

/**
 * TouchArea component for better touch interactions
 * 
 * Provides enhanced touch feedback for mobile devices with customizable effects
 * 
 * @component
 * @example
 * <TouchArea>
 *   <Button>Click me</Button>
 * </TouchArea>
 * 
 * @example
 * <TouchArea scale hover>
 *   <div>Hover and tap me</div>
 * </TouchArea>
 */
const TouchArea = forwardRef<HTMLDivElement, TouchAreaProps>(
  ({ 
    className, 
    children, 
    disableEffects = false,
    largeArea = false,
    subtle = false,
    glow = false,
    ripple = false,
    scale = true,
    rotate = false,
    bounce = false,
    press = true,
    hover = true,
    tap = true,
    focus = false,
    active = false,
    disabled = false,
    loading = false,
    success = false,
    error = false,
    warning = false,
    info = false,
    primary = false,
    secondary = false,
    tertiary = false,
    quaternary = false,
    quinary = false,
    senary = false,
    septenary = false,
    octonary = false,
    nonary = false,
    denary = false,
    ...props 
  }, ref) => {
    const { isTouch } = useResponsive();
    
    // Determine if we should apply touch effects
    const shouldApplyEffects = !disableEffects && (isTouch || hover || tap);
    
    // Configure motion variants based on props
    const motionProps: MotionProps = shouldApplyEffects ? {
      whileHover: hover ? { 
        scale: scale ? (subtle ? 1.02 : 1.05) : 1,
        rotate: rotate ? (subtle ? 0.5 : 1) : 0,
        y: bounce ? (subtle ? -2 : -5) : 0,
        boxShadow: glow ? '0 0 8px rgba(0, 102, 255, 0.5)' : undefined,
      } : undefined,
      whileTap: tap ? { 
        scale: press ? (subtle ? 0.98 : 0.95) : 1,
        rotate: rotate ? (subtle ? -0.5 : -1) : 0,
        boxShadow: 'none',
      } : undefined,
      transition: { 
        type: 'spring', 
        stiffness: 400, 
        damping: 17 
      }
    } : {};
    
    return (
      <motion.div
        ref={ref}
        className={cn(
          'touch-manipulation',
          largeArea && 'p-2 -m-2',
          disabled && 'opacity-50 pointer-events-none',
          className
        )}
        {...motionProps}
        {...props}
      >
        {children}
      </motion.div>
    );
  }
);

TouchArea.displayName = 'TouchArea';

export { TouchArea };
