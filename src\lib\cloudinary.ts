/**
 * Cloudinary configuration and utility functions
 */

// Replace these with your actual Cloudinary credentials
const CLOUDINARY_CLOUD_NAME = 'dqaib1ahd'; // e.g., 'dhruvcloud'
const CLOUDINARY_UPLOAD_PRESET = 'trading_journal_uploads'; // e.g., 'trading_journal_uploads'

/**
 * Uploads an image to Cloudinary
 * @param file The file to upload
 * @returns The uploaded image URL or null if upload failed
 */
export const uploadImageToCloudinary = async (file: File): Promise<string | null> => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('upload_preset', CLOUDINARY_UPLOAD_PRESET);

    const response = await fetch(
      `https://api.cloudinary.com/v1_1/${CLOUDINARY_CLOUD_NAME}/image/upload`,
      {
        method: 'POST',
        body: formData,
      }
    );

    if (!response.ok) {
      throw new Error(`Upload failed with status: ${response.status}`);
    }

    const data = await response.json();
    return data.secure_url;
  } catch (error) {
    console.error('Error uploading image to Cloudinary:', error);
    return null;
  }
};

/**
 * Validates if a file is an acceptable image
 * @param file The file to validate
 * @returns True if the file is valid, false otherwise
 */
export const validateImageFile = (file: File): boolean => {
  // Check file type
  const acceptedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  if (!acceptedTypes.includes(file.type)) {
    return false;
  }

  // Check file size (max 2MB)
  const maxSize = 2 * 1024 * 1024; // 2MB
  if (file.size > maxSize) {
    return false;
  }

  return true;
};
