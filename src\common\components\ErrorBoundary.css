.error-boundary {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  padding: 1rem;
  position: relative;
  z-index: 50;
}

.error-container {
  background: rgba(10, 26, 47, 0.8);
  border-radius: 0.75rem;
  border: 1px solid rgba(239, 68, 68, 0.4);
  padding: 2rem;
  max-width: 500px;
  text-align: center;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.error-container h2 {
  color: rgba(239, 68, 68, 0.9);
  margin-bottom: 1rem;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-container p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1.5rem;
}

.error-container details {
  margin-bottom: 1.5rem;
  text-align: left;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(0, 0, 0, 0.2);
  padding: 0.75rem;
  border-radius: 0.5rem;
}

.error-container summary {
  cursor: pointer;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.error-stack {
  font-family: monospace;
  font-size: 0.75rem;
  white-space: pre-wrap;
  overflow-x: auto;
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 0.25rem;
  margin-top: 0.5rem;
  max-height: 150px;
  overflow-y: auto;
}

.retry-button {
  background: rgba(14, 165, 233, 0.2);
  color: rgba(14, 165, 233, 0.9);
  border: 1px solid rgba(14, 165, 233, 0.3);
  padding: 0.5rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.retry-button:hover {
  background: rgba(14, 165, 233, 0.3);
  border-color: rgba(14, 165, 233, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
