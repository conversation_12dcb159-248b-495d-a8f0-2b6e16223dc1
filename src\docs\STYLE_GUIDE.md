# TradeChampionX Style Guide

This document outlines the coding standards and best practices for the TradeChampionX frontend codebase.

## Component Structure

All components should follow this structure:
1. Imports (external libraries first, then internal)
2. Type definitions and interfaces
3. Component with JSDoc
4. State and hooks
5. Helper functions and handlers
6. Computed values and classes
7. Render
8. Export

See `src/docs/ComponentTemplate.tsx` for an example.

## Naming Conventions

### Files and Directories

- **Components**: PascalCase (e.g., `Button.tsx`, `TradeHistory.tsx`)
- **Hooks**: camelCase with 'use' prefix (e.g., `useTradeHistory.ts`)
- **Utilities**: camelCase (e.g., `formatCurrency.ts`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `TRADE_TYPES.ts`)
- **Types/Interfaces**: PascalCase (e.g., `TradeTypes.ts`)

### Variables and Functions

- **Components**: PascalCase (e.g., `const Button = () => {}`)
- **Functions**: camelCase (e.g., `const handleClick = () => {}`)
- **Variables**: camelCase (e.g., `const isActive = true`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `const MAX_TRADES = 100`)
- **Interfaces/Types**: PascalCase with descriptive names (e.g., `interface ButtonProps`)
- **Boolean variables**: Use 'is', 'has', or 'should' prefix (e.g., `isLoading`, `hasError`)

## CSS and Styling

- Use Tailwind CSS classes for styling
- Use the `cn()` utility for conditional class names
- Group related classes together
- For complex components, consider extracting class combinations into variables

```tsx
// Good
const buttonClasses = cn(
  'px-4 py-2 rounded',
  isActive && 'bg-blue-500',
  isDisabled && 'opacity-50 cursor-not-allowed'
);

// Avoid long inline class strings
// Bad
<button className="px-4 py-2 rounded bg-blue-500 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
```

## JSDoc Comments

- All components should have JSDoc comments
- Include a brief description, @component tag, and usage examples
- Document all props with descriptions and default values
- Add @status tag to indicate component stability

```tsx
/**
 * Button component with multiple variants
 * 
 * @component
 * @status stable
 * 
 * @example
 * <Button variant="primary">Click me</Button>
 */
```

## Imports and Exports

- Use named exports for utilities and hooks
- Use default exports for components
- Group imports by external/internal
- Use absolute imports with aliases

```tsx
// Good
import React from 'react';
import { motion } from 'framer-motion';

import { Button } from '@/common/components/ui';
import { useAuth } from '@/common/hooks';
```

## State Management

- Use React hooks for component state
- Keep state as close to where it's used as possible
- Use context for global state that needs to be accessed by many components
- Consider using React Query for server state

## Error Handling

- Use try/catch blocks for async operations
- Display user-friendly error messages
- Log errors for debugging
- Handle edge cases and loading states

## Accessibility

- Use semantic HTML elements
- Include proper ARIA attributes
- Ensure keyboard navigation works
- Maintain sufficient color contrast

## Performance

- Memoize expensive calculations with useMemo
- Use useCallback for functions passed to child components
- Avoid unnecessary re-renders with React.memo when appropriate
- Use virtualization for long lists
