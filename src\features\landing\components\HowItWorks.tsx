import { <PERSON><PERSON><PERSON><PERSON>, Co<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ChevronRight, Info } from "lucide-react";
import { cn } from "@/common/utils";
import { But<PERSON> } from "@/common/components/ui/button";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/common/components/ui/tooltip";
import { Link } from "react-router-dom";

const steps = [
  {
    id: 1,
    title: "Join a Challenge",
    description: "Choose from daily, weekly or monthly challenges that match your trading style.",
    detailedInfo: "Our challenges are designed for all trading styles with flexible timeframes. Daily challenges are perfect for active traders, while weekly and monthly options suit different strategies and time commitments.",
    icon: CheckCircle,
    iconBg: "bg-forex-primary",
    cta: {
      text: "View Challenges",
      action: "pricing"
    }
  },
  {
    id: 2,
    title: "Pay with Crypto",
    description: "Quick and secure payment with your favorite cryptocurrency via NOWPayments.",
    detailedInfo: "We accept multiple cryptocurrencies through our secure NOWPayments integration. Transactions are processed quickly with minimal fees, allowing you to join challenges immediately.",
    icon: Coins,
    iconBg: "bg-forex-secondary",
  },
  {
    id: 3,
    title: "Connect cTrader",
    description: "Link your cTrader demo account with our simple OAuth connection.",
    detailedInfo: "Our platform integrates directly with cTrader using secure OAuth. This allows us to track your trading performance automatically without requiring access to your funds.",
    icon: BarChart,
    iconBg: "bg-forex-accent",
  },
  {
    id: 4,
    title: "Trade & Compete",
    description: "Follow our fair rules and climb the leaderboard with your trading skills.",
    detailedInfo: "Trade according to our transparent rules and compete against other traders. Your performance is tracked in real-time and displayed on our leaderboards.",
    icon: LineChart,
    iconBg: "bg-forex-primary",
    cta: {
      text: "View Rules",
      action: "rules"
    }
  },
  {
    id: 5,
    title: "Win Rewards",
    description: "Top performers receive prizes instantly with next-day payouts.",
    detailedInfo: "Winners receive their rewards within 24 hours. No withdrawal games or hidden clauses - if you win, you get paid promptly via your preferred cryptocurrency.",
    icon: Trophy,
    iconBg: "bg-amber-500",
  }
];

const HowItWorks = () => {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  // Define different theme options
  const themes = {
    current: {
      sectionBg: "bg-gradient-to-b from-forex-dark to-forex-card",
      badgeBg: "bg-forex-primary/10",
      badgeText: "text-forex-primary",
      badgeBorder: "border-forex-primary/20",
      headingText: "heading-primary",
      descriptionText: "paragraph-bright",
      timelineBg: "bg-gradient-to-b from-forex-primary via-forex-accent to-amber-500",
      cardBg: "bg-forex-card/60",
      cardBorder: "border-forex-light/10"
    },
    competitive: {
      sectionBg: "bg-gradient-to-br from-[#0f1c2e] via-[#1a2c4c] to-[#0f1c2e]",
      badgeBg: "bg-blue-500/20",
      badgeText: "text-blue-400",
      badgeBorder: "border-blue-500/30",
      headingText: "text-blue-100",
      descriptionText: "text-blue-200/80",
      timelineBg: "bg-gradient-to-b from-blue-500 via-indigo-500 to-purple-600",
      cardBg: "bg-[#1a2c4c]/80",
      cardBorder: "border-blue-400/10"
    },
    energetic: {
      sectionBg: "bg-gradient-to-br from-[#240019] via-[#3d0022] to-[#240019]",
      badgeBg: "bg-red-500/20",
      badgeText: "text-red-400",
      badgeBorder: "border-red-500/30",
      headingText: "text-red-100",
      descriptionText: "text-red-100/80",
      timelineBg: "bg-gradient-to-b from-red-600 via-orange-500 to-yellow-500",
      cardBg: "bg-[#3d0022]/80",
      cardBorder: "border-red-400/10"
    },
    futuristic: {
      sectionBg: "bg-gradient-to-br from-[#0a192f] via-[#172a46] to-[#0a192f]",
      badgeBg: "bg-cyan-500/20",
      badgeText: "text-cyan-400",
      badgeBorder: "border-cyan-500/30",
      headingText: "text-cyan-100",
      descriptionText: "text-cyan-100/80",
      timelineBg: "bg-gradient-to-b from-cyan-500 via-teal-500 to-emerald-500",
      cardBg: "bg-[#172a46]/80",
      cardBorder: "border-cyan-400/10"
    }
  };

  // Choose which theme to use - change this to try different themes
  const activeTheme = themes.competitive;

  return (
    <section className={`py-12 ${activeTheme.sectionBg}`} id="how-it-works">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-10" data-aos="fade-up">
          <span className={`inline-block px-4 py-1 ${activeTheme.badgeBg} ${activeTheme.badgeText} font-medium rounded-full mb-4 shadow-sm backdrop-blur-sm border ${activeTheme.badgeBorder}`}>
            Simple Process
          </span>
          <h2 className={`text-3xl md:text-4xl font-bold ${activeTheme.headingText} mb-3`}>
            How It Works
          </h2>
          <p className={`text-lg ${activeTheme.descriptionText}`}>
            Start your trading challenge journey in just a few simple steps
          </p>
        </div>

        <div className="relative max-w-4xl mx-auto">
          {/* Timeline connector for desktop */}
          <div className={`hidden md:block absolute left-1/2 top-8 bottom-8 w-1 ${activeTheme.timelineBg} transform -translate-x-1/2 z-0`}></div>

          {steps.map((step, index) => (
            <div
              key={step.id}
              className="relative z-10"
              data-aos={index % 2 === 0 ? "fade-right" : "fade-left"}
              data-aos-delay={index * 100}
            >
              <div className={`mb-8 md:mb-12 flex ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'} items-center`}>
                {/* Progress indicator */}
                <div className="absolute -left-2 md:left-1/2 md:-translate-x-1/2 top-0 md:top-1/2 md:-translate-y-1/2 flex items-center justify-center z-20">
                  <div className={`w-6 h-6 rounded-full ${activeTheme === themes.current ? 'bg-forex-dark border-2 border-forex-primary' :
                    activeTheme === themes.competitive ? 'bg-[#0f1c2e] border-2 border-blue-500' :
                    activeTheme === themes.energetic ? 'bg-[#240019] border-2 border-red-500' :
                    'bg-[#0a192f] border-2 border-cyan-500'} flex items-center justify-center text-xs font-bold text-white`}>
                    {step.id}
                  </div>
                </div>

                <div className={`w-full md:w-1/2 ${index % 2 === 0 ? 'md:pr-8 md:text-right' : 'md:pl-8'}`}>
                  <div className={`flex items-center mb-2 ${index % 2 === 0 ? 'md:flex-row-reverse' : ''}`}>
                    <span className={`${activeTheme.badgeText} font-semibold text-sm mb-1`}>
                      Step {step.id}
                    </span>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button
                            className={`${activeTheme === themes.current ? 'text-forex-light/50 hover:text-forex-primary focus:ring-forex-primary' :
                              activeTheme === themes.competitive ? 'text-blue-200/50 hover:text-blue-400 focus:ring-blue-500' :
                              activeTheme === themes.energetic ? 'text-red-200/50 hover:text-red-400 focus:ring-red-500' :
                              'text-cyan-200/50 hover:text-cyan-400 focus:ring-cyan-500'}
                              focus:outline-none focus:ring-2 focus:ring-opacity-50 rounded-full ${index % 2 === 0 ? 'md:mr-2' : 'ml-2'}`}
                            aria-label={`More info about ${step.title}`}
                          >
                            <Info className="w-4 h-4" />
                          </button>
                        </TooltipTrigger>
                        <TooltipContent side="top" className={`max-w-xs ${
                          activeTheme === themes.current ? 'bg-forex-dark border-forex-primary/20 text-forex-light' :
                          activeTheme === themes.competitive ? 'bg-[#0f1c2e] border-blue-500/20 text-blue-100' :
                          activeTheme === themes.energetic ? 'bg-[#240019] border-red-500/20 text-red-100' :
                          'bg-[#0a192f] border-cyan-500/20 text-cyan-100'} border p-3`}>
                          <p>{step.detailedInfo}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <h3 className={`text-xl font-semibold mb-2 ${activeTheme.headingText}`}>
                    {step.title}
                  </h3>
                  <p className={`${activeTheme.descriptionText} mb-3`}>
                    {step.description}
                  </p>
                  {step.cta && (
                    <div className={`${index % 2 === 0 ? 'md:text-right' : ''}`}>
                      <Button
                        variant="link"
                        className={`${
                          activeTheme === themes.current ? 'text-forex-primary hover:text-forex-accent' :
                          activeTheme === themes.competitive ? 'text-blue-400 hover:text-blue-300' :
                          activeTheme === themes.energetic ? 'text-red-400 hover:text-red-300' :
                          'text-cyan-400 hover:text-cyan-300'} p-0 h-auto font-medium`}
                        onClick={() => scrollToSection(step.cta.action)}
                      >
                        {step.cta.text}
                        <ChevronRight className="w-4 h-4 ml-1" />
                      </Button>
                    </div>
                  )}
                </div>

                <div className="hidden md:flex items-center justify-center w-0 md:w-10">
                  <div className={cn(
                    "flex items-center justify-center w-10 h-10 rounded-full",
                    activeTheme === themes.current ? step.iconBg :
                    activeTheme === themes.competitive ? (index % 2 === 0 ? 'bg-blue-500' : 'bg-indigo-500') :
                    activeTheme === themes.energetic ? (index % 2 === 0 ? 'bg-red-500' : 'bg-orange-500') :
                    (index % 2 === 0 ? 'bg-cyan-500' : 'bg-teal-500'),
                    activeTheme === themes.current ? 'shadow-glow-primary' : 'shadow-lg'
                  )}>
                    <step.icon className="w-5 h-5 text-white" />
                  </div>
                </div>

                <div className={`w-full md:w-1/2 ${index % 2 === 0 ? 'md:pl-8' : 'md:pr-8 md:text-right'}`}>
                  {/* Mobile icon positioning */}
                  <div className="flex md:hidden items-center mb-4">
                    <div className={cn(
                      "flex items-center justify-center w-8 h-8 rounded-full mr-3",
                      activeTheme === themes.current ? step.iconBg :
                      activeTheme === themes.competitive ? 'bg-blue-500' :
                      activeTheme === themes.energetic ? 'bg-red-500' :
                      'bg-cyan-500'
                    )}>
                      <step.icon className="w-4 h-4 text-white" />
                    </div>
                  </div>

                  <div className={`${activeTheme.cardBg} backdrop-blur-sm rounded-lg p-4 transform transition-all hover:shadow-md border ${activeTheme.cardBorder} ${index % 2 === 0 ? 'md:rounded-tr-2xl' : 'md:rounded-tl-2xl'}`}>
                    <div className={`h-20 rounded-lg ${
                      activeTheme === themes.current ? `${step.iconBg}/10` :
                      activeTheme === themes.competitive ? 'bg-blue-500/10' :
                      activeTheme === themes.energetic ? 'bg-red-500/10' :
                      'bg-cyan-500/10'
                    } flex items-center justify-center`}>
                      <step.icon className={`w-10 h-10 ${
                        activeTheme === themes.current ? (step.iconBg === 'bg-amber-500' ? 'text-amber-500' : 'text-forex-primary') :
                        activeTheme === themes.competitive ? 'text-blue-400' :
                        activeTheme === themes.energetic ? 'text-red-400' :
                        'text-cyan-400'
                      }`} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-8" data-aos="fade-up">
          <Button
            className={`${
              activeTheme === themes.current ? 'bg-gradient-to-r from-forex-primary to-forex-accent' :
              activeTheme === themes.competitive ? 'bg-gradient-to-r from-blue-600 to-indigo-600' :
              activeTheme === themes.energetic ? 'bg-gradient-to-r from-red-600 to-orange-500' :
              'bg-gradient-to-r from-cyan-600 to-teal-600'
            } hover:opacity-90 text-white shadow-lg`}
            onClick={() => scrollToSection("pricing")}
          >
            Start Your Challenge Now
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
