/* Import mobile optimizations */
@import './common/styles/mobile-optimizations.css';

/* Import dialog fixes for UI freezing issues */
@import './common/styles/dialog-fixes.css';

/* Import custom animations */
@import './styles/animations.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 217.2 32.6% 17.5%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --success: 142.1 76.2% 36.3%;
    --success-foreground: 210 40% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;

    --sidebar: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-accent: 217.2 32.6% 17.5%;

    --danger: 0 62.8% 30.6%;

    --radius: 0.5rem;
  }

  .light {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --success: 142.1 76.2% 36.3%;
    --success-foreground: 210 40% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    --sidebar: 0 0% 100%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-accent: 210 40% 96.1%;

    --danger: 0 84.2% 60.2%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Custom Font Settings */
  h1, h2, h3, h4, h5, h6, .font-display {
    font-family: 'Manrope', sans-serif;
    font-feature-settings: "cv11", "cv01", "cv02", "cv03", "cv04";
    letter-spacing: -0.025em;
    font-weight: 600;
  }

  /* Remove underlines from links containing images or logos */
  a:has(img), a:has(svg) {
    text-decoration: none !important;
  }
}

/* Modern DeFi Design System - Inspired by Linea, Uniswap, Aave */
:root {
  /* Primary Brand Colors - Sophisticated gradients */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;
  --primary-950: #082f49;

  /* Accent Colors - Electric & Modern */
  --accent-purple: #8b5cf6;
  --accent-pink: #ec4899;
  --accent-orange: #f97316;
  --accent-cyan: #06b6d4;
  --accent-emerald: #10b981;

  /* Neutral Palette - Premium grays */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;
  --neutral-950: #0a0a0a;

  /* Dark Theme - Primary backgrounds */
  --bg-primary: #0a0a0a;
  --bg-secondary: #111111;
  --bg-tertiary: #1a1a1a;
  --bg-quaternary: #262626;

  /* Glass & Blur Effects */
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);

  /* Text Hierarchy */
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.8);
  --text-tertiary: rgba(255, 255, 255, 0.6);
  --text-quaternary: rgba(255, 255, 255, 0.4);

  /* Status Colors */
  --success: #10b981;
  --success-bg: rgba(16, 185, 129, 0.1);
  --error: #ef4444;
  --error-bg: rgba(239, 68, 68, 0.1);
  --warning: #f59e0b;
  --warning-bg: rgba(245, 158, 11, 0.1);
  --info: #3b82f6;
  --info-bg: rgba(59, 130, 246, 0.1);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-500) 0%, var(--accent-purple) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--accent-cyan) 0%, var(--accent-emerald) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-pink) 0%, var(--accent-orange) 100%);
  --gradient-dark: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-glow: 0 0 20px rgba(14, 165, 233, 0.3);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Spacing Scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* Legacy support - keeping old variables for compatibility */
  --forex-primary: var(--primary-600);
  --forex-secondary: var(--accent-emerald);
  --forex-accent: var(--primary-500);
  --forex-dark: var(--bg-primary);
  --forex-card: var(--bg-tertiary);
  --forex-border: var(--neutral-700);
  --forex-light: var(--neutral-50);
  --forex-neutral: var(--neutral-500);
  --forex-hover: var(--primary-700);
  --forex-profit: var(--success);
  --forex-loss: var(--error);
  --forex-muted: var(--neutral-400);

  /* Text colors for dark backgrounds */
  --text-on-dark: var(--text-primary);
  --text-on-dark-muted: var(--text-secondary);
  --text-on-dark-subtle: var(--text-tertiary);

  /* Text colors for light backgrounds */
  --text-on-light: var(--neutral-900);
  --text-on-light-muted: var(--neutral-600);
  --text-on-light-subtle: var(--neutral-500);

  /* Card backgrounds */
  --card-dark-bg: var(--glass-bg);
  --card-light-bg: rgba(255, 255, 255, 0.9);
}

/* Tailwind Custom Colors */
.bg-forex-primary { background-color: var(--forex-primary); }
.bg-forex-secondary { background-color: var(--forex-secondary); }
.bg-forex-accent { background-color: var(--forex-accent); }
.bg-forex-dark { background-color: var(--forex-dark); }
.bg-forex-card { background-color: var(--forex-card); }
.bg-forex-border { background-color: var(--forex-border); }
.bg-forex-light { background-color: var(--forex-light); }
.bg-forex-neutral { background-color: var(--forex-neutral); }
.bg-forex-hover { background-color: var(--forex-hover); }
.bg-forex-profit { background-color: var(--forex-profit); }
.bg-forex-loss { background-color: var(--forex-loss); }

.text-forex-primary { color: var(--forex-primary); }
.text-forex-secondary { color: var(--forex-secondary); }
.text-forex-accent { color: var(--forex-accent); }
.text-forex-dark { color: var(--forex-dark); }
.text-forex-card { color: var(--forex-card); }
.text-forex-border { color: var(--forex-border); }
.text-forex-light { color: #ffffff !important; } /* Force white text */
.text-forex-neutral { color: #ffffff !important; } /* Force white text */
.text-forex-hover { color: var(--forex-hover); }
.text-forex-profit { color: var(--forex-profit); }
.text-forex-loss { color: var(--forex-loss); }

/* Text colors for different backgrounds */
.text-on-dark { color: var(--text-on-dark); }
.text-on-dark-muted { color: var(--text-on-dark-muted); }
.text-on-light { color: var(--text-on-light); }
.text-on-light-muted { color: var(--text-on-light-muted); }

.border-forex-primary { border-color: var(--forex-primary); }
.border-forex-secondary { border-color: var(--forex-secondary); }
.border-forex-accent { border-color: var(--forex-accent); }
.border-forex-dark { border-color: var(--forex-dark); }
.border-forex-card { border-color: var(--forex-card); }
.border-forex-border { border-color: var(--forex-border); }
.border-forex-light { border-color: var(--forex-light); }
.border-forex-neutral { border-color: var(--forex-neutral); }
.border-forex-hover { border-color: var(--forex-hover); }
.border-forex-profit { border-color: var(--forex-profit); }
.border-forex-loss { border-color: var(--forex-loss); }

/* Modern Component Utilities */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--glass-shadow);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.5);
}

.glass-card-light {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
}

/* Premium Button Styles */
.btn-primary {
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--radius-lg);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(14, 165, 233, 0.4);
}

/* Gradient Text */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-secondary {
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Modern Card Variants */
.card-modern {
  background: var(--bg-tertiary);
  border: 1px solid var(--neutral-800);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-modern:hover {
  border-color: var(--primary-500);
  box-shadow: var(--shadow-glow);
  transform: translateY(-4px);
}

.card-premium {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--space-xl);
  position: relative;
  overflow: hidden;
}

.card-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--gradient-primary);
}

/* Status Indicators */
.status-success {
  background: var(--success-bg);
  color: var(--success);
  border: 1px solid var(--success);
}

.status-error {
  background: var(--error-bg);
  color: var(--error);
  border: 1px solid var(--error);
}

.status-warning {
  background: var(--warning-bg);
  color: var(--warning);
  border: 1px solid var(--warning);
}

.status-info {
  background: var(--info-bg);
  color: var(--info);
  border: 1px solid var(--info);
}

/* For leaderboard and data tables */
.data-row {
  color: var(--text-on-dark);
}

.data-row-alt {
  background-color: rgba(30, 41, 59, 0.3);
}

/* Badge styling */
.badge {
  background-color: var(--forex-primary);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-secondary {
  background-color: var(--forex-secondary);
}

.badge-accent {
  background-color: var(--forex-accent);
}

/* Card content styling */
.card-title {
  color: var(--text-on-dark);
  font-weight: 600;
  font-size: 1.125rem;
  margin-bottom: 0.5rem;
}

.card-subtitle {
  color: var(--text-on-dark-muted);
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.card-text {
  color: var(--text-on-dark-muted);
}

/* Light card content styling */
.card-light-title {
  color: var(--text-on-light);
  font-weight: 600;
  font-size: 1.125rem;
  margin-bottom: 0.5rem;
}

.card-light-subtitle {
  color: var(--text-on-light-muted);
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.card-light-text {
  color: var(--text-on-light-muted);
}

/* Modern Animation System */
.animate-slide-up {
  opacity: 0;
  transform: translateY(24px);
  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-slide-down {
  opacity: 0;
  transform: translateY(-24px);
  animation: slideDown 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-slide-left {
  opacity: 0;
  transform: translateX(24px);
  animation: slideLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-slide-right {
  opacity: 0;
  transform: translateX(-24px);
  animation: slideRight 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-fade-in {
  opacity: 0;
  animation: fadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-scale-in {
  opacity: 0;
  transform: scale(0.95);
  animation: scaleIn 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin-slow {
  animation: spin 3s linear infinite;
}

.animate-bounce-subtle {
  animation: bounceSubtle 2s infinite;
}

/* Shimmer Effect - Modern */
.shimmer {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Glow Effects */
.glow-primary {
  box-shadow: 0 0 20px rgba(14, 165, 233, 0.3);
  animation: glowPulse 2s ease-in-out infinite alternate;
}

.glow-success {
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  animation: glowPulse 2s ease-in-out infinite alternate;
}

.glow-error {
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  animation: glowPulse 2s ease-in-out infinite alternate;
}

/* Hover Animations */
.hover-lift {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-scale {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-glow:hover {
  box-shadow: 0 0 30px rgba(14, 165, 233, 0.4);
}

/* Progress Bar Hover Effect */
.progress-hover:hover .progress-fill {
  filter: brightness(1.1);
}

/* Card Hover Effects */
.card-hover-effect {
  transition: all 0.3s ease;
}

.card-hover-effect:hover {
  box-shadow: 0 10px 15px -3px hsla(var(--primary), 0.1);
  border-color: hsla(var(--primary), 0.2);
  transform: scale(1.01);
}

/* Modern Keyframe Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(24px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-24px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeft {
  from {
    opacity: 0;
    transform: translateX(24px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideRight {
  from {
    opacity: 0;
    transform: translateX(-24px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes bounceSubtle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes glowPulse {
  0% {
    box-shadow: 0 0 20px rgba(14, 165, 233, 0.2);
  }
  100% {
    box-shadow: 0 0 30px rgba(14, 165, 233, 0.4);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Gradient animations */
.bg-gradient-primary {
  background: linear-gradient(90deg, var(--forex-primary), var(--forex-accent));
}

.animate-gradient-x {
  background-size: 200% 100%;
  animation: gradient-x 8s ease infinite;
}

@keyframes gradient-x {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Text glow effects */
.text-glow-blue {
  color: var(--forex-primary);
  text-shadow: 0 0 10px rgba(2, 132, 199, 0.5);
}

.text-glow-green {
  color: var(--forex-profit);
  text-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
}

/* Card hover effects */
.card-hover-effect {
  transition: all 0.3s ease;
}

.card-hover-effect:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(2, 132, 199, 0.3);
}

/* Dashboard specific styles */
.bg-forex-darker {
  background-color: #0f172a; /* Darker than forex-dark */
}

.bg-forex-warning {
  background-color: #f59e0b; /* Amber warning color */
}

.text-forex-warning {
  color: #f59e0b; /* Amber warning color */
}

/* Animated background elements */
.animated-bg-gradient {
  background: linear-gradient(
    45deg,
    rgba(2, 132, 199, 0.05) 0%,
    rgba(14, 165, 233, 0.05) 25%,
    rgba(16, 185, 129, 0.05) 50%,
    rgba(14, 165, 233, 0.05) 75%,
    rgba(2, 132, 199, 0.05) 100%
  );
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.8);
}
