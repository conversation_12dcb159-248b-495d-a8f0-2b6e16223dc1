import React, { useState, useRef, useEffect } from "react";
import { Search, ChevronDown, ChevronUp } from "lucide-react";
import { Input } from "@/common/components/ui/input";

interface FAQItem {
  question: string;
  answer: string;
  category: string;
}

const FAQSection: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [filteredFAQs, setFilteredFAQs] = useState<FAQItem[]>(faqs);
  const answerRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Filter FAQs based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredFAQs(faqs);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = faqs.filter(
      (faq) =>
        faq.question.toLowerCase().includes(query) ||
        faq.answer.toLowerCase().includes(query) ||
        faq.category.toLowerCase().includes(query)
    );
    setFilteredFAQs(filtered);
    setActiveIndex(null);
  }, [searchQuery]);

  // Toggle FAQ item
  const toggleFAQ = (index: number) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  // Group FAQs by category
  const groupedFAQs: { [key: string]: FAQItem[] } = {};
  filteredFAQs.forEach((faq) => {
    if (!groupedFAQs[faq.category]) {
      groupedFAQs[faq.category] = [];
    }
    groupedFAQs[faq.category].push(faq);
  });

  return (
    <section className="py-20 bg-forex-dark">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <span className="inline-block px-4 py-1 bg-forex-primary/20 text-forex-primary font-medium rounded-full mb-4">
            Support Center
          </span>
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Frequently Asked Questions
          </h2>
          <p className="text-lg text-forex-light/80">
            Find answers to common questions about our trading challenges
          </p>
        </div>

        {/* Search Bar */}
        <div className="max-w-2xl mx-auto mb-12">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
              <Search className="h-5 w-5 text-forex-light/50" />
            </div>
            <Input
              type="search"
              placeholder="Search for answers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-12 py-6 bg-forex-card/30 border-forex-border/20 text-white focus:border-forex-primary focus:ring-forex-primary/20 rounded-xl"
            />
          </div>
        </div>

        {/* FAQ Content */}
        <div className="max-w-4xl mx-auto">
          {Object.keys(groupedFAQs).length === 0 ? (
            <div className="text-center py-10 bg-forex-card/30 rounded-xl border border-forex-border/20">
              <p className="text-forex-light mb-2">No results found for "{searchQuery}"</p>
              <p className="text-sm text-forex-light/60">Try different keywords or browse all questions</p>
            </div>
          ) : (
            Object.entries(groupedFAQs).map(([category, items], categoryIndex) => (
              <div key={categoryIndex} className="mb-10">
                <h3 className="text-xl font-bold text-white mb-6 flex items-center">
                  <span className="w-2 h-8 bg-gradient-to-b from-forex-primary to-forex-accent rounded-full mr-3"></span>
                  {category}
                </h3>
                <div className="space-y-4">
                  {items.map((faq, index) => {
                    const globalIndex = filteredFAQs.findIndex(
                      (item) => item.question === faq.question && item.category === faq.category
                    );
                    return (
                      <div
                        key={index}
                        className="bg-forex-card/30 backdrop-blur-sm border border-forex-border/20 rounded-xl overflow-hidden transition-all duration-300 hover:border-forex-primary/30"
                      >
                        <button
                          className="w-full px-6 py-5 flex justify-between items-center text-left focus:outline-none"
                          onClick={() => toggleFAQ(globalIndex)}
                        >
                          <h4 className="text-lg font-medium text-white pr-8">
                            {highlightText(faq.question, searchQuery)}
                          </h4>
                          <div className="flex-shrink-0">
                            {activeIndex === globalIndex ? (
                              <ChevronUp className="h-5 w-5 text-forex-primary" />
                            ) : (
                              <ChevronDown className="h-5 w-5 text-forex-light/50" />
                            )}
                          </div>
                        </button>
                        <div
                          ref={(el) => (answerRefs.current[globalIndex] = el)}
                          className={`px-6 overflow-hidden transition-all duration-300 ${
                            activeIndex === globalIndex ? "pb-6" : "max-h-0"
                          }`}
                          style={{
                            maxHeight: activeIndex === globalIndex ? answerRefs.current[globalIndex]?.scrollHeight + "px" : "0px",
                          }}
                        >
                          <div className="text-forex-light/80 prose prose-invert prose-sm max-w-none">
                            {highlightText(faq.answer, searchQuery)}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))
          )}
        </div>

        {/* Contact Support */}
        <div className="max-w-4xl mx-auto mt-16">
          <div className="bg-gradient-to-r from-forex-primary/20 to-forex-accent/20 rounded-xl p-8 border border-forex-border/20 text-center">
            <h3 className="text-2xl font-bold text-white mb-3">Still have questions?</h3>
            <p className="text-forex-light/80 mb-6 max-w-2xl mx-auto">
              Our support team is ready to help with any questions about our trading challenges that aren't covered here.
            </p>
            <a
              href="#"
              className="inline-block px-8 py-4 bg-gradient-to-r from-forex-primary to-forex-accent text-white rounded-lg hover:opacity-90 transition-opacity font-medium"
            >
              Contact Support
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

// Helper function to highlight search terms
const highlightText = (text: string, query: string) => {
  if (!query.trim()) return text;

  const parts = text.split(new RegExp(`(${query})`, 'gi'));
  return (
    <>
      {parts.map((part, i) =>
        part.toLowerCase() === query.toLowerCase() ? (
          <span key={i} className="bg-forex-primary/20 text-forex-primary px-1 rounded">
            {part}
          </span>
        ) : (
          part
        )
      )}
    </>
  );
};

// FAQ data
const faqs: FAQItem[] = [
  {
    question: "How is TradeChampionX different from prop firms?",
    answer: "Unlike traditional prop firms that profit when traders fail, our community-focused model distributes 75% of entry fees directly to top performers. We have transparent rules, no hidden disqualification clauses, no restrictive trading windows, and guaranteed payouts with no withdrawal hoops to jump through.",
    category: "About TradeChampionX"
  },
  {
    question: "What types of challenges do you offer?",
    answer: "We offer daily, weekly, and monthly challenges, each with different entry fees, prize pools, and rule sets. Daily challenges are perfect for quick results, weekly challenges offer a balance of time and reward, while monthly challenges provide the largest prize pools and most flexible trading conditions.",
    category: "About TradeChampionX"
  },
  {
    question: "Can I participate in multiple challenges simultaneously?",
    answer: "Yes, you can participate in multiple challenges at the same time. Each challenge is tracked separately, allowing you to test different strategies across different timeframes. Many of our successful traders participate in daily, weekly, and monthly challenges concurrently to maximize their earning potential.",
    category: "About TradeChampionX"
  },
  {
    question: "How does the dynamic prize pool work?",
    answer: "Our prize pool grows with each participant. 75% of all entry fees go directly to the community: 60% to top performers (1st, 2nd, 3rd place) and 15% to wallet credits for the top 30% of traders. This creates a truly community-driven reward system where everyone benefits from growth. For example, in a daily challenge with 100 participants at $20 each, the total pool would be $2,000, with $1,500 going to the community and $500 to platform operations.",
    category: "Prizes & Rewards"
  },
  {
    question: "How are prizes distributed?",
    answer: "Prize distribution varies by challenge type. For daily challenges, 50% goes to 1st place, 30% to 2nd, and 20% to 3rd. Weekly challenges distribute 40% to 1st, 25% to 2nd, 15% to 3rd, and 20% split among 4th-10th places. Monthly challenges have a broader distribution with additional participation credits.",
    category: "Prizes & Rewards"
  },
  {
    question: "What are wallet credits and how do they work?",
    answer: "Wallet credits are our internal currency awarded to top 30% performers in each challenge. You can use these credits to enter future challenges or combine them with cryptocurrency for partial payments. Credits expire after 30 days of inactivity, so be sure to use them regularly. If you finish in the top 30% of a challenge with 100 participants and a $2,000 prize pool, you might receive around $12-15 in wallet credits depending on your placement.",
    category: "Prizes & Rewards"
  },
  {
    question: "How do I connect my cTrader account?",
    answer: "After registering and paying for a challenge, you'll be redirected to cTrader's OAuth page. Simply log in with your cTrader credentials and authorize our application. We'll securely store your access token and automatically sync your trading data in real-time throughout the challenge period.",
    category: "Technical"
  },
  {
    question: "What happens if there's an issue with my cTrader connection?",
    answer: "If we detect an issue with your cTrader connection, we'll automatically attempt to refresh your access token. If problems persist, you'll receive a notification via Discord. Our system includes automatic retry mechanisms, and our support team is available to help resolve any connection issues quickly.",
    category: "Technical"
  },
  {
    question: "How often is the leaderboard updated?",
    answer: "The leaderboard is updated in real-time whenever a trader closes a position. This ensures you have immediate tracking of your standing against other participants. All trading metrics are pulled directly from cTrader to guarantee accuracy and prevent manipulation.",
    category: "Technical"
  },
  {
    question: "What happens if I exceed the maximum drawdown?",
    answer: "If you exceed the maximum drawdown limit (4% for daily challenges, 6% for weekly challenges, 10% for monthly challenges), you'll be automatically disqualified. You'll receive a notification via Discord, and your status on the leaderboard will be updated. Unlike prop firms, we don't use hidden rules or manipulate markets to force disqualification.",
    category: "Rules & Requirements"
  },
  {
    question: "Are there any trading restrictions during challenges?",
    answer: "Unlike prop firms with restrictive trading requirements, we allow trading of all major forex pairs, cryptocurrencies, indices, and commodities available on cTrader. There are no specific session restrictions and no manipulated spreads or slippage. You're free to trade as you would in your personal account, while following the challenge rules.",
    category: "Rules & Requirements"
  },
  {
    question: "What are the minimum trading requirements?",
    answer: "Requirements vary by challenge type. Daily challenges require at least 2 trades. Weekly challenges require at least 3 trades. Monthly challenges require either 6 different trading days OR 3 swing trades held for at least 2 days each. These requirements ensure fair competition and prevent lucky single-trade wins.",
    category: "Rules & Requirements"
  },
  {
    question: "What payment methods do you accept?",
    answer: "We accept cryptocurrency payments through NOWPayments. This includes Bitcoin, Ethereum, and several other popular cryptocurrencies. The payment process is secure and typically confirms within minutes, allowing you to start trading immediately after connecting your cTrader account.",
    category: "Payments & Accounts"
  },
  {
    question: "Can I use wallet credits for partial payments?",
    answer: "Yes, you can combine wallet credits with cryptocurrency payments when entering challenges. The system will automatically apply your available credits and only charge the remaining amount in cryptocurrency, making it easier to participate in multiple challenges.",
    category: "Payments & Accounts"
  },
  {
    question: "How do I connect my Discord account?",
    answer: "After registering, you'll receive instructions to connect your Discord account. Once connected, you'll automatically receive the 'Verified Challenger' role in our Discord community. This gives you access to exclusive channels for challenge discussion, trading tips, and special announcements.",
    category: "Community"
  },
  {
    question: "What benefits does the Discord community offer?",
    answer: "Our Discord community provides real-time challenge updates, trading discussions with fellow participants, direct access to our support team, educational content, and exclusive announcements. Active community members also receive special perks and early access to new features.",
    category: "Community"
  }
];

export default FAQSection;
