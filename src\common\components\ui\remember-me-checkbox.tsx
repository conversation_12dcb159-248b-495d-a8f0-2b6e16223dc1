import React, { useState } from "react";
import { Checkbox, Label } from "@/common/components/ui";

/**
 * RememberMeCheckbox component for authentication forms
 *
 * A styled checkbox that allows users to opt into extended session duration.
 * When checked, the user's session will be remembered.
 *
 * @component
 * @status stable
 * @version 1.0.0
 *
 * @example
 * // Basic usage
 * <RememberMeCheckbox onChange={(checked) => console.log(checked)} />
 */
interface RememberMeCheckboxProps {
  onChange?: (checked: boolean) => void;
  defaultChecked?: boolean;
}

const RememberMeCheckbox: React.FC<RememberMeCheckboxProps> = ({
  onChange,
  defaultChecked = false
}) => {
  const [checked, setChecked] = useState(defaultChecked);

  const handleChange = (checked: boolean) => {
    setChecked(checked);
    if (onChange) {
      onChange(checked);
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <Checkbox
        id="remember-me"
        checked={checked}
        onCheckedChange={handleChange}
        className="border-gray-300"
      />
      <Label
        htmlFor="remember-me"
        className="text-sm text-black cursor-pointer font-medium"
      >
        Remember me
      </Label>
    </div>
  );
};

export default RememberMeCheckbox;
