
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TradeChampionX - Fair Trading Challenges</title>
    <meta name="description" content="Join daily, weekly, and monthly trading challenges with clear rules and guaranteed payouts. No hidden clauses, no withdrawal games." />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
  </head>
  <body class="dark">
    <div id="root"></div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
    <script>
      // Force dark mode
      document.documentElement.classList.add('dark');
      document.body.classList.add('dark');
    </script>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        if (typeof AOS !== 'undefined') {
          AOS.init({
            duration: 800,
            once: false,
            mirror: true
          });
        }
      });
    </script>

    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
